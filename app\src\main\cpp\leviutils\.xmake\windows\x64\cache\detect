{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-L"] = true,
            ["--whole-archive"] = true,
            ["--disable-nxcompat"] = true,
            ["--export-all-symbols"] = true,
            ["-dn"] = true,
            ["--no-dynamicbase"] = true,
            ["--strip-all"] = true,
            ["--no-demangle"] = true,
            ["--high-entropy-va"] = true,
            ["--disable-dynamicbase"] = true,
            ["--large-address-aware"] = true,
            ["-s"] = true,
            ["-m"] = true,
            ["--help"] = true,
            ["-o"] = true,
            ["--dynamicbase"] = true,
            ["--Bstatic"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--tsaware"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--disable-tsaware"] = true,
            ["--version"] = true,
            ["--Bdynamic"] = true,
            ["--exclude-all-symbols"] = true,
            ["--enable-auto-import"] = true,
            ["--no-seh"] = true,
            ["--appcontainer"] = true,
            ["--shared"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--insert-timestamp"] = true,
            ["-dy"] = true,
            ["--no-fatal-warnings"] = true,
            ["--verbose"] = true,
            ["-S"] = true,
            ["-static"] = true,
            ["--no-whole-archive"] = true,
            ["--kill-at"] = true,
            ["-v"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--nxcompat"] = true,
            ["--gc-sections"] = true,
            ["-l"] = true,
            ["--disable-no-seh"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-gc-sections"] = true,
            ["--strip-debug"] = true,
            ["--demangle"] = true,
            ["--fatal-warnings"] = true,
            ["--disable-auto-import"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-CC"] = true,
            ["-shared-libsan"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fexceptions"] = true,
            ["-fgnu89-inline"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-E"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-index-header-map"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fsanitize-stats"] = true,
            ["-fprotect-parens"] = true,
            ["-fno-offload-lto"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-mno-memops"] = true,
            ["-print-targets"] = true,
            ["-D"] = true,
            ["-include"] = true,
            ["-ffast-math"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-imacros"] = true,
            ["-fborland-extensions"] = true,
            ["-flto"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-mno-extern-sdata"] = true,
            ["-ffixed-x8"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fmath-errno"] = true,
            ["-mno-hvx"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-C"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-b"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-gcodeview"] = true,
            ["-fcs-profile-generate"] = true,
            ["-ffixed-r19"] = true,
            ["-fno-exceptions"] = true,
            ["-fgnu-keywords"] = true,
            ["-fpascal-strings"] = true,
            ["-fwasm-exceptions"] = true,
            ["-B"] = true,
            ["-Qn"] = true,
            ["-Qy"] = true,
            ["-Xanalyzer"] = true,
            ["-include-pch"] = true,
            ["-c"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-nostdinc"] = true,
            ["-fno-standalone-debug"] = true,
            ["-ffixed-x20"] = true,
            ["-isysroot"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-global-isel"] = true,
            ["-fno-temp-file"] = true,
            ["-fshort-enums"] = true,
            ["-mno-implicit-float"] = true,
            ["-fobjc-arc"] = true,
            ["-dD"] = true,
            ["-fxray-link-deps"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fno-declspec"] = true,
            ["-cl-opt-disable"] = true,
            ["-fgpu-rdc"] = true,
            ["-finline-hint-functions"] = true,
            ["-ffixed-x9"] = true,
            ["-freroll-loops"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fdeclspec"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fasync-exceptions"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["--precompile"] = true,
            ["-fno-jump-tables"] = true,
            ["-fno-lto"] = true,
            ["-MMD"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fembed-bitcode"] = true,
            ["-idirafter"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-integrated-as"] = true,
            ["-Ttext"] = true,
            ["-pg"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-nobuiltininc"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-ffixed-x30"] = true,
            ["-ffixed-x27"] = true,
            ["-ffixed-x11"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-ffixed-a3"] = true,
            ["-mno-cumode"] = true,
            ["-mpacked-stack"] = true,
            ["-fsanitize-trap"] = true,
            ["-fno-memory-profile"] = true,
            ["-gdwarf-2"] = true,
            ["-nogpuinc"] = true,
            ["-fdigraphs"] = true,
            ["-ffixed-a1"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fno-finite-loops"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-ffixed-x16"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-mibt-seal"] = true,
            ["-fno-show-column"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-ffixed-d1"] = true,
            ["-dI"] = true,
            ["-Wdeprecated"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-ffixed-x24"] = true,
            ["-gdwarf64"] = true,
            ["-fms-extensions"] = true,
            ["-fno-signed-char"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-MD"] = true,
            ["-fmodules-search-all"] = true,
            ["-fstack-protector"] = true,
            ["-mmt"] = true,
            ["-fno-rtti"] = true,
            ["-MG"] = true,
            ["-ffixed-x26"] = true,
            ["--hip-link"] = true,
            ["-fno-signed-zeros"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-gdwarf-5"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fno-unroll-loops"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fno-sycl"] = true,
            ["-fno-strict-return"] = true,
            ["-pedantic"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-Xlinker"] = true,
            ["-mexecute-only"] = true,
            ["-ffixed-x4"] = true,
            ["-mrelax-all"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-mno-packets"] = true,
            ["-meabi"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-gdwarf-4"] = true,
            ["-ffixed-x29"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-mhvx"] = true,
            ["--version"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fsycl"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-print-multiarch"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-gdwarf"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-ffixed-d4"] = true,
            ["-mcode-object-v3"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-Xclang"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-membedded-data"] = true,
            ["-Tbss"] = true,
            ["-mextern-sdata"] = true,
            ["-gline-directives-only"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-pthread"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-ffunction-sections"] = true,
            ["-fropi"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-mt"] = true,
            ["-fminimize-whitespace"] = true,
            ["-undef"] = true,
            ["-moutline-atomics"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fobjc-weak"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["--verify-debug-info"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fno-operator-names"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-profile-generate"] = true,
            ["-dsym-dir"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-msave-restore"] = true,
            ["-fno-rtti-data"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-ffixed-a0"] = true,
            ["-fno-elide-type"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-ffixed-x22"] = true,
            ["-mno-madd4"] = true,
            ["-fvectorize"] = true,
            ["-fstack-size-section"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fdebug-types-section"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-Xpreprocessor"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-mno-code-object-v3"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fno-split-stack"] = true,
            ["-ivfsoverlay"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-MV"] = true,
            ["-mmsa"] = true,
            ["-iwithsysroot"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["--cuda-device-only"] = true,
            ["-cxx-isystem"] = true,
            ["-ffixed-x31"] = true,
            ["-ffixed-x28"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-static-libsan"] = true,
            ["-help"] = true,
            ["-arch"] = true,
            ["-ffixed-x19"] = true,
            ["-mrtd"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-isystem"] = true,
            ["-mcrc"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-save-stats"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-Xopenmp-target"] = true,
            ["-T"] = true,
            ["-working-directory"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fcommon"] = true,
            ["-mnvs"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fnew-infallible"] = true,
            ["-ffixed-d2"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-gdwarf-3"] = true,
            ["--analyze"] = true,
            ["-U"] = true,
            ["-traditional-cpp"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-ftime-trace"] = true,
            ["-fgnu-runtime"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-mllvm"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fms-compatibility"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-addrsig"] = true,
            ["-mno-movt"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-moutline"] = true,
            ["-ffixed-x13"] = true,
            ["-mnocrc"] = true,
            ["-ffixed-x7"] = true,
            ["-ibuiltininc"] = true,
            ["-trigraphs"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-ffixed-x12"] = true,
            ["-fverbose-asm"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-stack-protector"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-mno-seses"] = true,
            ["-ffixed-r9"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fseh-exceptions"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fcall-saved-x13"] = true,
            ["-cl-no-stdinc"] = true,
            ["-ffixed-d5"] = true,
            ["-mmark-bti-property"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fno-digraphs"] = true,
            ["-fstack-clash-protection"] = true,
            ["-P"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mno-long-calls"] = true,
            ["-mno-gpopt"] = true,
            ["-ffixed-x10"] = true,
            ["-mpackets"] = true,
            ["-fsplit-stack"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-debug-macro"] = true,
            ["-mcmse"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-femulated-tls"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-print-ivar-layout"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-mlong-double-64"] = true,
            ["-iwithprefix"] = true,
            ["-emit-llvm"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-funroll-loops"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fopenmp"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fzvector"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fmodules"] = true,
            ["-dependency-dot"] = true,
            ["-ffixed-x18"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-foffload-lto"] = true,
            ["-ffixed-x6"] = true,
            ["-frwpi"] = true,
            ["-MT"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mno-outline"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fcall-saved-x12"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-dM"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-MQ"] = true,
            ["-fstack-protector-all"] = true,
            ["-malign-double"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fsigned-char"] = true,
            ["-fcxx-modules"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-ffixed-x23"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-S"] = true,
            ["-ffixed-x3"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fno-pch-codegen"] = true,
            ["-iquote"] = true,
            ["-rpath"] = true,
            ["-fpch-codegen"] = true,
            ["--help-hidden"] = true,
            ["-o"] = true,
            ["-MJ"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fcall-saved-x9"] = true,
            ["-emit-merged-ifs"] = true,
            ["-faligned-allocation"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-mno-restrict-it"] = true,
            ["-ffixed-x1"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mglobal-merge"] = true,
            ["-fconvergent-functions"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-print-runtime-dir"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fopenmp-simd"] = true,
            ["-fapprox-func"] = true,
            ["-mabicalls"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fintegrated-as"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-new-infallible"] = true,
            ["-miamcu"] = true,
            ["-mlocal-sdata"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-MM"] = true,
            ["-mbackchain"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-finline-functions"] = true,
            ["-mfp64"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-L"] = true,
            ["-fno-short-wchar"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-print-search-dirs"] = true,
            ["-mgpopt"] = true,
            ["-fcoroutines-ts"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-mfp32"] = true,
            ["-mrelax"] = true,
            ["-freciprocal-math"] = true,
            ["-mthread-model"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fno-unique-section-names"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fno-plt"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-gno-embed-source"] = true,
            ["-mno-outline-atomics"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-mrecord-mcount"] = true,
            ["-gembed-source"] = true,
            ["-rewrite-objc"] = true,
            ["-MP"] = true,
            ["-mno-msa"] = true,
            ["-z"] = true,
            ["-ffixed-point"] = true,
            ["-M"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-mnop-mcount"] = true,
            ["-fno-fixed-point"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mno-save-restore"] = true,
            ["--migrate"] = true,
            ["-relocatable-pch"] = true,
            ["-ffreestanding"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-cl-mad-enable"] = true,
            ["-femit-all-decls"] = true,
            ["-gmodules"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-ffixed-x17"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fignore-exceptions"] = true,
            ["-mlong-double-80"] = true,
            ["-MF"] = true,
            ["-fblocks"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fdata-sections"] = true,
            ["-fdebug-macro"] = true,
            ["-static-openmp"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-mcumode"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-gdwarf32"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-print-target-triple"] = true,
            ["-fsized-deallocation"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-verify-pch"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-mfentry"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-module-file-info"] = true,
            ["-msvr4-struct-return"] = true,
            ["-mmemops"] = true,
            ["-ffixed-d6"] = true,
            ["-fstack-usage"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-ffixed-a6"] = true,
            ["-fjump-tables"] = true,
            ["-fno-trigraphs"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-ffixed-x2"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fcall-saved-x15"] = true,
            ["-gline-tables-only"] = true,
            ["-Tdata"] = true,
            ["-ftrapv"] = true,
            ["-mnvj"] = true,
            ["-faddrsig"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fstrict-enums"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-H"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-mno-embedded-data"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffinite-loops"] = true,
            ["-fapple-kext"] = true,
            ["-ffixed-x21"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-ffixed-x15"] = true,
            ["--cuda-host-only"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fshort-wchar"] = true,
            ["-mlong-double-128"] = true,
            ["-fsystem-module"] = true,
            ["-mno-tgsplit"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fno-discard-value-names"] = true,
            ["--analyzer-output"] = true,
            ["-ffixed-a4"] = true,
            ["-fno-access-control"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-show-source-location"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-mno-crc"] = true,
            ["-time"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-print-resource-dir"] = true,
            ["-ffixed-d7"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fapplication-extension"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-iprefix"] = true,
            ["-fstandalone-debug"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-w"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-Xassembler"] = true,
            ["-mrestrict-it"] = true,
            ["-fcxx-exceptions"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fobjc-exceptions"] = true,
            ["-mlong-calls"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fglobal-isel"] = true,
            ["-emit-module"] = true,
            ["-fslp-vectorize"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-mms-bitfields"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fmodules-ts"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-I"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-ftrigraphs"] = true,
            ["-fmodules-decluse"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fcf-protection"] = true,
            ["-fno-use-init-array"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mno-global-merge"] = true,
            ["-mtgsplit"] = true,
            ["-extract-api"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-v"] = true,
            ["-ffixed-d3"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mno-nvs"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-ffixed-x25"] = true,
            ["-dependency-file"] = true,
            ["-print-supported-cpus"] = true,
            ["-mno-nvj"] = true,
            ["-fno-spell-checking"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fno-common"] = true,
            ["--no-cuda-version-check"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-ffixed-a2"] = true,
            ["-fpcc-struct-return"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-save-temps"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fansi-escape-codes"] = true,
            ["-ffixed-d0"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-nogpulib"] = true,
            ["-G"] = true,
            ["-fuse-line-directives"] = true,
            ["-fmemory-profile"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-msoft-float"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-finstrument-functions"] = true,
            ["-print-effective-triple"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-mseses"] = true,
            ["-module-dependency-dir"] = true,
            ["-fenable-matrix"] = true,
            ["-freg-struct-return"] = true,
            ["-mlvi-hardening"] = true,
            ["-mlvi-cfi"] = true,
            ["-munaligned-access"] = true,
            ["-pipe"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-emit-ast"] = true,
            ["-mmadd4"] = true,
            ["-fno-autolink"] = true,
            ["-ffixed-a5"] = true,
            ["--config"] = true,
            ["-ffixed-x5"] = true,
            ["-fxray-instrument"] = true,
            ["-F"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-x"] = true,
            ["-isystem-after"] = true,
            ["-mstackrealign"] = true,
            ["-g"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-elide-constructors"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fms-hotpatch"] = true,
            ["-I-"] = true,
            ["-mno-abicalls"] = true,
            ["-fno-builtin"] = true,
            ["-ffixed-x14"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-mno-relax"] = true
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sdkver = "21",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            cross = "arm-linux-androideabi-",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    }
}