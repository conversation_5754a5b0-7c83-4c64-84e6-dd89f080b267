{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mcumode"] = true,
            ["-mhvx"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-mtgsplit"] = true,
            ["-mno-cumode"] = true,
            ["-fglobal-isel"] = true,
            ["-g"] = true,
            ["-cl-opt-disable"] = true,
            ["-fmath-errno"] = true,
            ["-fno-standalone-debug"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-mlong-double-128"] = true,
            ["-mno-hvx"] = true,
            ["-moutline"] = true,
            ["-pg"] = true,
            ["-I"] = true,
            ["-fjump-tables"] = true,
            ["-mexecute-only"] = true,
            ["-ffixed-r19"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-undef"] = true,
            ["-ffixed-x8"] = true,
            ["--analyze"] = true,
            ["-mrelax"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-ffixed-x23"] = true,
            ["-maix-struct-return"] = true,
            ["-fxray-link-deps"] = true,
            ["-mstackrealign"] = true,
            ["-fno-signed-char"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-mno-relax"] = true,
            ["-trigraphs"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-ffixed-x21"] = true,
            ["-fcxx-exceptions"] = true,
            ["-save-stats"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-serialize-diagnostics"] = true,
            ["-iquote"] = true,
            ["-traditional-cpp"] = true,
            ["-ffixed-a2"] = true,
            ["-mstack-arg-probe"] = true,
            ["-cl-no-stdinc"] = true,
            ["-print-multiarch"] = true,
            ["-nogpulib"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fexceptions"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mno-global-merge"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-MMD"] = true,
            ["-isystem"] = true,
            ["-fno-addrsig"] = true,
            ["-L"] = true,
            ["-fno-new-infallible"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fcall-saved-x18"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-Tbss"] = true,
            ["-I-"] = true,
            ["-mllvm"] = true,
            ["-include-pch"] = true,
            ["-ffixed-x16"] = true,
            ["-ffixed-a4"] = true,
            ["-mms-bitfields"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-mlong-calls"] = true,
            ["-M"] = true,
            ["-fsystem-module"] = true,
            ["-mmt"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-mibt-seal"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fslp-vectorize"] = true,
            ["-fno-show-column"] = true,
            ["-msave-restore"] = true,
            ["-fno-short-wchar"] = true,
            ["-F"] = true,
            ["-ffixed-x11"] = true,
            ["-fprotect-parens"] = true,
            ["-ffixed-r9"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-ffixed-a5"] = true,
            ["-fstack-protector-all"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mno-madd4"] = true,
            ["-MJ"] = true,
            ["-mhvx-qfloat"] = true,
            ["-rpath"] = true,
            ["-fdigraphs"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-G"] = true,
            ["-print-effective-triple"] = true,
            ["-time"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-ffast-math"] = true,
            ["-mno-implicit-float"] = true,
            ["-mrtd"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-finstrument-functions"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-ffinite-loops"] = true,
            ["-MM"] = true,
            ["-ffixed-x25"] = true,
            ["-fintegrated-as"] = true,
            ["-gno-embed-source"] = true,
            ["-ffixed-x5"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-ffixed-x20"] = true,
            ["-fno-integrated-as"] = true,
            ["-Qn"] = true,
            ["-mcrc"] = true,
            ["-mno-long-calls"] = true,
            ["-fseh-exceptions"] = true,
            ["-fmodules-decluse"] = true,
            ["-fno-autolink"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-ffixed-x14"] = true,
            ["-fstack-protector"] = true,
            ["-fno-split-stack"] = true,
            ["-mnvs"] = true,
            ["-mbackchain"] = true,
            ["-fnew-infallible"] = true,
            ["-ftrigraphs"] = true,
            ["-mcode-object-v3"] = true,
            ["-iwithsysroot"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-module-dependency-dir"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fmemory-profile"] = true,
            ["-fms-compatibility"] = true,
            ["-gdwarf64"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-frwpi"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-gdwarf-3"] = true,
            ["-fgpu-rdc"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-funroll-loops"] = true,
            ["-fstandalone-debug"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fzvector"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fno-strict-return"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-mno-execute-only"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-ffixed-x9"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-femit-all-decls"] = true,
            ["-static-openmp"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-D"] = true,
            ["-ftime-trace"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fstack-clash-protection"] = true,
            ["-mthread-model"] = true,
            ["-fno-operator-names"] = true,
            ["-help"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-gembed-source"] = true,
            ["-faddrsig"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-emit-llvm"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fpch-debuginfo"] = true,
            ["-print-ivar-layout"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fcall-saved-x14"] = true,
            ["-MT"] = true,
            ["-E"] = true,
            ["-mno-packets"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-digraphs"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-memory-profile"] = true,
            ["-fapple-kext"] = true,
            ["-Tdata"] = true,
            ["-MD"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fsanitize-stats"] = true,
            ["-MQ"] = true,
            ["-fno-declspec"] = true,
            ["-mlong-double-64"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-print-target-triple"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fno-jump-tables"] = true,
            ["-dependency-file"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fminimize-whitespace"] = true,
            ["-idirafter"] = true,
            ["-dI"] = true,
            ["-fvectorize"] = true,
            ["-gdwarf-5"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fasync-exceptions"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-freciprocal-math"] = true,
            ["-ffixed-x12"] = true,
            ["-mno-mt"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fno-access-control"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-mpacked-stack"] = true,
            ["-finline-hint-functions"] = true,
            ["-fborland-extensions"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fno-rtti"] = true,
            ["-w"] = true,
            ["-cxx-isystem"] = true,
            ["-membedded-data"] = true,
            ["-fkeep-static-consts"] = true,
            ["-mno-save-restore"] = true,
            ["-gdwarf-4"] = true,
            ["-shared-libsan"] = true,
            ["-pipe"] = true,
            ["-fverbose-asm"] = true,
            ["-fsplit-stack"] = true,
            ["-ffixed-x31"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-ffixed-a6"] = true,
            ["-fapprox-func"] = true,
            ["-v"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-ffixed-point"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-show-source-location"] = true,
            ["-ffixed-a1"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-mrelax-all"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fcxx-modules"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fno-offload-lto"] = true,
            ["-B"] = true,
            ["-mlong-double-80"] = true,
            ["-Ttext"] = true,
            ["-mextern-sdata"] = true,
            ["-mno-gpopt"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-Wdeprecated"] = true,
            ["-mno-restrict-it"] = true,
            ["-mno-outline-atomics"] = true,
            ["-freg-struct-return"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fstrict-enums"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fopenmp"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fno-unique-section-names"] = true,
            ["-ffixed-x2"] = true,
            ["-fapplication-extension"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["--gpu-bundle-output"] = true,
            ["-static-libsan"] = true,
            ["-fropi"] = true,
            ["-fintegrated-cc1"] = true,
            ["-mlvi-hardening"] = true,
            ["-fmodules-search-all"] = true,
            ["-mnop-mcount"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-merge-all-constants"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-save-temps"] = true,
            ["-fno-lto"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fdeclspec"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fcall-saved-x12"] = true,
            ["-emit-ast"] = true,
            ["-pedantic"] = true,
            ["-ffixed-x4"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-C"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-ivfsoverlay"] = true,
            ["-MF"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-fixed-point"] = true,
            ["-c"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-imacros"] = true,
            ["--cuda-device-only"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-module-file-info"] = true,
            ["-mmadd4"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-ffixed-x28"] = true,
            ["-freroll-loops"] = true,
            ["-fno-temp-file"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-mno-movt"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fcoverage-mapping"] = true,
            ["-ffixed-x17"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-Xpreprocessor"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fshort-enums"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fms-extensions"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-mrestrict-it"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-ffunction-sections"] = true,
            ["-mno-crc"] = true,
            ["--precompile"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-isysroot"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-b"] = true,
            ["-fcf-protection"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-relocatable-pch"] = true,
            ["-dD"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-ftrapv"] = true,
            ["-Xassembler"] = true,
            ["-fcommon"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-mmark-bti-property"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-MG"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fshort-wchar"] = true,
            ["-ffixed-x24"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-cl-mad-enable"] = true,
            ["-mfp32"] = true,
            ["--hip-link"] = true,
            ["--emit-static-lib"] = true,
            ["-mno-nvs"] = true,
            ["-mlvi-cfi"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-x"] = true,
            ["-emit-module"] = true,
            ["-emit-interface-stubs"] = true,
            ["-CC"] = true,
            ["-ffixed-x27"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-ffixed-x1"] = true,
            ["-extract-api"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-plt"] = true,
            ["-iwithprefix"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fno-exceptions"] = true,
            ["-o"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-ffixed-d2"] = true,
            ["-flto"] = true,
            ["-pthread"] = true,
            ["-fsigned-char"] = true,
            ["-iprefix"] = true,
            ["-index-header-map"] = true,
            ["-fpch-codegen"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mno-embedded-data"] = true,
            ["-mno-memops"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-arch"] = true,
            ["-ffixed-x18"] = true,
            ["-include"] = true,
            ["-print-targets"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fignore-exceptions"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fenable-matrix"] = true,
            ["-print-runtime-dir"] = true,
            ["-fno-profile-generate"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-mfentry"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-ffixed-x19"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-miamcu"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-mno-nvj"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fno-finite-loops"] = true,
            ["-P"] = true,
            ["-fdebug-macro"] = true,
            ["-fcall-saved-x13"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-ffixed-a0"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["--help-hidden"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-mseses"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-ffixed-x7"] = true,
            ["-fno-pch-codegen"] = true,
            ["-Xanalyzer"] = true,
            ["-gdwarf"] = true,
            ["-fobjc-arc"] = true,
            ["-mno-abicalls"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-ffixed-a3"] = true,
            ["-fno-builtin"] = true,
            ["-fpcc-struct-return"] = true,
            ["-nostdinc"] = true,
            ["-mlocal-sdata"] = true,
            ["-ffixed-d1"] = true,
            ["-nobuiltininc"] = true,
            ["-Qy"] = true,
            ["-ibuiltininc"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-MP"] = true,
            ["-ffixed-d6"] = true,
            ["-munaligned-access"] = true,
            ["-verify-pch"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fgnu89-inline"] = true,
            ["-dependency-dot"] = true,
            ["-fno-stack-protector"] = true,
            ["-fsized-deallocation"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-foffload-lto"] = true,
            ["-fpascal-strings"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-isystem-after"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-mno-seses"] = true,
            ["-ffixed-x26"] = true,
            ["-fansi-escape-codes"] = true,
            ["-meabi"] = true,
            ["-finline-functions"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-femulated-tls"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-emit-merged-ifs"] = true,
            ["-ffixed-x3"] = true,
            ["-fmerge-all-constants"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-ffixed-x30"] = true,
            ["-fobjc-exceptions"] = true,
            ["-gcodeview"] = true,
            ["-fuse-line-directives"] = true,
            ["-mno-local-sdata"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fno-debug-macro"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fmodules"] = true,
            ["-dM"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-dsym-dir"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fno-global-isel"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fgnu-keywords"] = true,
            ["-gmodules"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-print-search-dirs"] = true,
            ["-H"] = true,
            ["-fno-rtti-data"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-msoft-float"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fno-use-init-array"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-cl-finite-math-only"] = true,
            ["-S"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-mno-outline"] = true,
            ["-fms-hotpatch"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-nogpuinc"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-ffixed-d7"] = true,
            ["-mrecord-mcount"] = true,
            ["-fno-common"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fno-spell-checking"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["--analyzer-output"] = true,
            ["-fobjc-weak"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fprofile-generate"] = true,
            ["-mno-unaligned-access"] = true,
            ["--config"] = true,
            ["-gdwarf32"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fopenmp-simd"] = true,
            ["-ffixed-d0"] = true,
            ["-fdebug-types-section"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-T"] = true,
            ["-mpackets"] = true,
            ["-mglobal-merge"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-Xopenmp-target"] = true,
            ["--version"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-ffixed-x15"] = true,
            ["-ffixed-d3"] = true,
            ["-fgnu-runtime"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mfp64"] = true,
            ["-z"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fno-trigraphs"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mmemops"] = true,
            ["-mmsa"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fxray-instrument"] = true,
            ["-fembed-bitcode"] = true,
            ["--migrate"] = true,
            ["-fconvergent-functions"] = true,
            ["-U"] = true,
            ["-ffixed-x10"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-Xclang"] = true,
            ["-ffixed-x22"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-mnvj"] = true,
            ["-fmodules-ts"] = true,
            ["-mcmse"] = true,
            ["-MV"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-print-resource-dir"] = true,
            ["-ffixed-x29"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-Qunused-arguments"] = true,
            ["-mno-msa"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-mgpopt"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-gline-tables-only"] = true,
            ["-fstack-size-section"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fsanitize-trap"] = true,
            ["-faligned-allocation"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fblocks"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-ffixed-d5"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-malign-double"] = true,
            ["-mno-tgsplit"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fsycl"] = true,
            ["--cuda-host-only"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["--no-cuda-version-check"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fno-sycl"] = true,
            ["-print-supported-cpus"] = true,
            ["-fstack-usage"] = true,
            ["-ffreestanding"] = true,
            ["-Xlinker"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fdata-sections"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-mabicalls"] = true,
            ["-gline-directives-only"] = true,
            ["-mnocrc"] = true,
            ["-fgpu-sanitize"] = true,
            ["-working-directory"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-ffixed-x6"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-ffixed-d4"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-assume-sane-operator-new"] = true
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-fatal-warnings"] = true,
            ["-L"] = true,
            ["--high-entropy-va"] = true,
            ["--insert-timestamp"] = true,
            ["--no-demangle"] = true,
            ["-o"] = true,
            ["-s"] = true,
            ["-static"] = true,
            ["--nxcompat"] = true,
            ["-dy"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--disable-no-seh"] = true,
            ["--no-seh"] = true,
            ["--shared"] = true,
            ["--large-address-aware"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--allow-multiple-definition"] = true,
            ["--help"] = true,
            ["--kill-at"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["-v"] = true,
            ["-dn"] = true,
            ["--disable-nxcompat"] = true,
            ["--demangle"] = true,
            ["-m"] = true,
            ["--exclude-all-symbols"] = true,
            ["--no-gc-sections"] = true,
            ["--fatal-warnings"] = true,
            ["--disable-auto-import"] = true,
            ["--disable-dynamicbase"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--tsaware"] = true,
            ["--Bstatic"] = true,
            ["--no-dynamicbase"] = true,
            ["-l"] = true,
            ["--whole-archive"] = true,
            ["-S"] = true,
            ["--dynamicbase"] = true,
            ["--Bdynamic"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--version"] = true,
            ["--strip-debug"] = true,
            ["--verbose"] = true,
            ["--export-all-symbols"] = true,
            ["--gc-sections"] = true,
            ["--no-whole-archive"] = true,
            ["--enable-auto-import"] = true,
            ["--strip-all"] = true,
            ["--disable-tsaware"] = true,
            ["--appcontainer"] = true
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            cross = "arm-linux-androideabi-",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21"
        }
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true
    }
}