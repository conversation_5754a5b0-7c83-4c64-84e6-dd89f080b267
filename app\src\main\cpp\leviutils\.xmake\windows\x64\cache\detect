{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-new-infallible"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-Tbss"] = true,
            ["-mrelax-all"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-profile-generate"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fseh-exceptions"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-static-libsan"] = true,
            ["-fno-signed-char"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-mno-abicalls"] = true,
            ["-ffixed-a0"] = true,
            ["-Wdeprecated"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-short-wchar"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-I-"] = true,
            ["-B"] = true,
            ["-mno-save-restore"] = true,
            ["-mfentry"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fsigned-char"] = true,
            ["-fno-declspec"] = true,
            ["-mpackets"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mno-embedded-data"] = true,
            ["-fignore-exceptions"] = true,
            ["-nobuiltininc"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-dM"] = true,
            ["-ffixed-x3"] = true,
            ["-ffixed-d7"] = true,
            ["-ffixed-x8"] = true,
            ["-imacros"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fxray-link-deps"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-mms-bitfields"] = true,
            ["-mrecord-mcount"] = true,
            ["-fsplit-stack"] = true,
            ["-Tdata"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-mllvm"] = true,
            ["-fno-addrsig"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-ffixed-x13"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fno-fixed-point"] = true,
            ["-mno-global-merge"] = true,
            ["-fprotect-parens"] = true,
            ["-ffixed-x25"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-gdwarf64"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["--no-cuda-version-check"] = true,
            ["-mthread-model"] = true,
            ["-ffixed-x14"] = true,
            ["-fropi"] = true,
            ["-fno-plt"] = true,
            ["-relocatable-pch"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-mno-cumode"] = true,
            ["-working-directory"] = true,
            ["-fdigraphs"] = true,
            ["-fobjc-weak"] = true,
            ["--hip-link"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-ffixed-x9"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fno-show-source-location"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-ffixed-x12"] = true,
            ["-fno-rtti"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fsystem-module"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-ivfsoverlay"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fdebug-types-section"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-mcmse"] = true,
            ["-I"] = true,
            ["-fintegrated-as"] = true,
            ["-freciprocal-math"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fcall-saved-x10"] = true,
            ["-print-resource-dir"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-gdwarf"] = true,
            ["-iwithsysroot"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-mnvs"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-ftime-trace"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-iprefix"] = true,
            ["-fms-extensions"] = true,
            ["-mlvi-cfi"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-mnvj"] = true,
            ["-mmemops"] = true,
            ["--precompile"] = true,
            ["-fno-finite-loops"] = true,
            ["-ffixed-a2"] = true,
            ["-mno-mt"] = true,
            ["-ffixed-d1"] = true,
            ["-mmsa"] = true,
            ["-flto"] = true,
            ["-mcrc"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-mno-relax"] = true,
            ["-ffixed-d0"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mhvx"] = true,
            ["-mpacked-stack"] = true,
            ["-include"] = true,
            ["-msoft-float"] = true,
            ["-fsanitize-trap"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fapprox-func"] = true,
            ["-ffixed-x15"] = true,
            ["-ffixed-x27"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-mno-movt"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["--cuda-host-only"] = true,
            ["-fdebug-macro"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fno-jump-tables"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-mextern-sdata"] = true,
            ["-fprofile-generate"] = true,
            ["-fconvergent-functions"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-index-header-map"] = true,
            ["-fmath-errno"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mibt-seal"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fno-spell-checking"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fverbose-asm"] = true,
            ["-MMD"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-ffixed-a6"] = true,
            ["-help"] = true,
            ["-ffixed-x30"] = true,
            ["-rewrite-objc"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-module-file-info"] = true,
            ["-mrestrict-it"] = true,
            ["-fcall-saved-x12"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-Qn"] = true,
            ["-miamcu"] = true,
            ["-ffixed-r9"] = true,
            ["-serialize-diagnostics"] = true,
            ["-w"] = true,
            ["-fborland-extensions"] = true,
            ["-ffixed-x20"] = true,
            ["-mno-restrict-it"] = true,
            ["-print-effective-triple"] = true,
            ["-mno-nvs"] = true,
            ["-fno-stack-protector"] = true,
            ["-fdeclspec"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fpascal-strings"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-malign-double"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-S"] = true,
            ["-mno-outline"] = true,
            ["-mno-msa"] = true,
            ["-fmodules-search-all"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-mcode-object-v3"] = true,
            ["-fmodules"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fno-elide-constructors"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fslp-vectorize"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fshort-wchar"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-gcodeview"] = true,
            ["-fstandalone-debug"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fmodules-decluse"] = true,
            ["-mlong-double-80"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-faligned-allocation"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fno-lto"] = true,
            ["-v"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-ffixed-x24"] = true,
            ["-T"] = true,
            ["-fuse-line-directives"] = true,
            ["-ffixed-d6"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fsycl"] = true,
            ["-fmodules-ts"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-MJ"] = true,
            ["-ffixed-x18"] = true,
            ["-ibuiltininc"] = true,
            ["-fcxx-exceptions"] = true,
            ["-pg"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-gembed-source"] = true,
            ["-fno-split-stack"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-time"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fapplication-extension"] = true,
            ["-MQ"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-debug-macro"] = true,
            ["-ffixed-x23"] = true,
            ["-emit-ast"] = true,
            ["-fstack-protector"] = true,
            ["-frwpi"] = true,
            ["-fno-offload-lto"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-gdwarf-5"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fno-digraphs"] = true,
            ["--migrate"] = true,
            ["-mno-memops"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-sycl"] = true,
            ["-fno-unique-section-names"] = true,
            ["-ffixed-x11"] = true,
            ["-mno-lvi-cfi"] = true,
            ["--verify-debug-info"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-ffixed-x31"] = true,
            ["-dsym-dir"] = true,
            ["-ffixed-x5"] = true,
            ["-mexecute-only"] = true,
            ["-gline-tables-only"] = true,
            ["-fembed-bitcode"] = true,
            ["-isysroot"] = true,
            ["-fcf-protection"] = true,
            ["-ffast-math"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-cl-mad-enable"] = true,
            ["-ffixed-x19"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-o"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fgnu-runtime"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mmark-bti-property"] = true,
            ["-MP"] = true,
            ["-fstack-usage"] = true,
            ["-ffunction-sections"] = true,
            ["-iwithprefixbefore"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-nostdinc"] = true,
            ["-dependency-file"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-dD"] = true,
            ["-gline-directives-only"] = true,
            ["-ffixed-d3"] = true,
            ["-mrtd"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-ffixed-a4"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fgnu-keywords"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-mno-nvj"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-access-control"] = true,
            ["-mgpopt"] = true,
            ["--help-hidden"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-MT"] = true,
            ["-print-target-triple"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-rpath"] = true,
            ["-MM"] = true,
            ["-fno-profile-instr-use"] = true,
            ["--emit-static-lib"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fsanitize-stats"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fwritable-strings"] = true,
            ["-b"] = true,
            ["-G"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-mlong-double-128"] = true,
            ["-L"] = true,
            ["-maix-struct-return"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fno-signed-zeros"] = true,
            ["-ffixed-x7"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-nogpulib"] = true,
            ["-mwavefrontsize64"] = true,
            ["--config"] = true,
            ["-fno-rtti-data"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fpch-codegen"] = true,
            ["-fexceptions"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fglobal-isel"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mstack-arg-probe"] = true,
            ["-mrelax"] = true,
            ["-fno-global-isel"] = true,
            ["-arch"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-MF"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-Xpreprocessor"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fcxx-modules"] = true,
            ["-mlvi-hardening"] = true,
            ["-emit-module"] = true,
            ["-print-supported-cpus"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fgnu89-inline"] = true,
            ["-finline-functions"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fpcc-struct-return"] = true,
            ["-membedded-data"] = true,
            ["-ffixed-x6"] = true,
            ["-ffixed-x28"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-freroll-loops"] = true,
            ["-mlong-calls"] = true,
            ["-mhvx-qfloat"] = true,
            ["-pipe"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-finstrument-functions"] = true,
            ["-save-stats"] = true,
            ["-mno-madd4"] = true,
            ["--analyzer-output"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mno-unaligned-access"] = true,
            ["-shared-libsan"] = true,
            ["-Xclang"] = true,
            ["-mno-seses"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fasync-exceptions"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fno-strict-return"] = true,
            ["-fopenmp-simd"] = true,
            ["-fvectorize"] = true,
            ["-cl-opt-disable"] = true,
            ["--analyze"] = true,
            ["-fcall-saved-x14"] = true,
            ["-meabi"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fansi-escape-codes"] = true,
            ["-MV"] = true,
            ["-ffixed-x21"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-ffixed-x4"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-emit-llvm"] = true,
            ["-fkeep-static-consts"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-gcodeview-ghash"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-munaligned-access"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fforce-enable-int128"] = true,
            ["-mnocrc"] = true,
            ["-msave-restore"] = true,
            ["-Ttext"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-faddrsig"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-g"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-module-dependency-dir"] = true,
            ["-ffixed-d2"] = true,
            ["-mno-neg-immediates"] = true,
            ["-mno-gpopt"] = true,
            ["-mcumode"] = true,
            ["-undef"] = true,
            ["-mno-long-calls"] = true,
            ["-mlong-double-64"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fenable-matrix"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-ffreestanding"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-mglobal-merge"] = true,
            ["-fsave-optimization-record"] = true,
            ["-gdwarf32"] = true,
            ["-MD"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-gdwarf-3"] = true,
            ["-print-multiarch"] = true,
            ["-fno-trigraphs"] = true,
            ["-mseses"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fshort-enums"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["--cuda-device-only"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-mno-tgsplit"] = true,
            ["-ffixed-x10"] = true,
            ["-fopenmp"] = true,
            ["-print-runtime-dir"] = true,
            ["-fno-show-column"] = true,
            ["-M"] = true,
            ["-print-targets"] = true,
            ["-E"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-mno-crc"] = true,
            ["-F"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fno-exceptions"] = true,
            ["-ffinite-loops"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-ffixed-x17"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-dependency-dot"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-femulated-tls"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-gdwarf-4"] = true,
            ["-mno-packets"] = true,
            ["-mlocal-sdata"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-funroll-loops"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-freg-struct-return"] = true,
            ["-mno-local-sdata"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-nogpuinc"] = true,
            ["-x"] = true,
            ["-Xassembler"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mmt"] = true,
            ["-verify-pch"] = true,
            ["-include-pch"] = true,
            ["-ffixed-point"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fzvector"] = true,
            ["-finline-hint-functions"] = true,
            ["-fms-compatibility"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-cxx-isystem"] = true,
            ["-mmadd4"] = true,
            ["-H"] = true,
            ["-mno-code-object-v3"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fapple-kext"] = true,
            ["-dI"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fxray-instrument"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-C"] = true,
            ["-ffixed-d4"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-foffload-lto"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-U"] = true,
            ["-fno-builtin"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-msvr4-struct-return"] = true,
            ["-mabicalls"] = true,
            ["-trigraphs"] = true,
            ["-ffixed-r19"] = true,
            ["-fno-temp-file"] = true,
            ["-mno-execute-only"] = true,
            ["-traditional-cpp"] = true,
            ["-iwithprefix"] = true,
            ["-fno-autolink"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fdata-sections"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-nohipwrapperinc"] = true,
            ["-moutline"] = true,
            ["-fstack-protector-all"] = true,
            ["-femit-all-decls"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-c"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fobjc-arc"] = true,
            ["-Xanalyzer"] = true,
            ["-gno-embed-source"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fno-use-init-array"] = true,
            ["-save-temps"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fno-operator-names"] = true,
            ["-pedantic"] = true,
            ["-isystem"] = true,
            ["-fobjc-exceptions"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-gmodules"] = true,
            ["-fmerge-all-constants"] = true,
            ["-P"] = true,
            ["-fcs-profile-generate"] = true,
            ["-ftrapv"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fms-hotpatch"] = true,
            ["-mstackrealign"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-ffixed-d5"] = true,
            ["-fgpu-rdc"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-static-openmp"] = true,
            ["-mtgsplit"] = true,
            ["-MG"] = true,
            ["-pthread"] = true,
            ["-mfp32"] = true,
            ["-ffixed-a3"] = true,
            ["-ffixed-x29"] = true,
            ["-mnop-mcount"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-iquote"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fblocks"] = true,
            ["-ffixed-x2"] = true,
            ["-ffixed-x22"] = true,
            ["-mno-implicit-float"] = true,
            ["-Xopenmp-target"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-mbackchain"] = true,
            ["-fcall-saved-x18"] = true,
            ["--version"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fstack-size-section"] = true,
            ["-fcommon"] = true,
            ["-fstrict-enums"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-z"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-ffixed-x26"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fmemory-profile"] = true,
            ["-isystem-after"] = true,
            ["-D"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fsized-deallocation"] = true,
            ["-Xlinker"] = true,
            ["-CC"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fnew-infallible"] = true,
            ["-fjump-tables"] = true,
            ["-mno-hvx"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mfp64"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-idirafter"] = true,
            ["-fstack-clash-protection"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-common"] = true,
            ["-extract-api"] = true,
            ["-ffixed-a1"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-Qy"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-Qunused-arguments"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true
        }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            sdkver = "21",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]]
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--demangle"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-insert-timestamp"] = true,
            ["--no-seh"] = true,
            ["--large-address-aware"] = true,
            ["--nxcompat"] = true,
            ["--strip-debug"] = true,
            ["--disable-no-seh"] = true,
            ["-static"] = true,
            ["--Bstatic"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--version"] = true,
            ["--no-demangle"] = true,
            ["-S"] = true,
            ["--kill-at"] = true,
            ["-dn"] = true,
            ["--Bdynamic"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--verbose"] = true,
            ["--shared"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["-o"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--no-fatal-warnings"] = true,
            ["--strip-all"] = true,
            ["--help"] = true,
            ["--gc-sections"] = true,
            ["--tsaware"] = true,
            ["--enable-auto-import"] = true,
            ["-l"] = true,
            ["-s"] = true,
            ["-v"] = true,
            ["--no-whole-archive"] = true,
            ["-dy"] = true,
            ["--disable-dynamicbase"] = true,
            ["--disable-tsaware"] = true,
            ["--fatal-warnings"] = true,
            ["-L"] = true,
            ["--high-entropy-va"] = true,
            ["-m"] = true,
            ["--no-dynamicbase"] = true,
            ["--disable-auto-import"] = true,
            ["--no-gc-sections"] = true,
            ["--exclude-all-symbols"] = true,
            ["--whole-archive"] = true,
            ["--disable-nxcompat"] = true,
            ["--appcontainer"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--dynamicbase"] = true,
            ["--export-all-symbols"] = true,
            ["--insert-timestamp"] = true
        }
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    }
}