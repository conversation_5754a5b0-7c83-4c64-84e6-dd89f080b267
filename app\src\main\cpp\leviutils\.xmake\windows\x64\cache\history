{
    cmdlines = {
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\armeabi-v7a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\Levi<PERSON>ixMaybe\app\src\main\jniLibs -a armeabi-v7a]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake clean -v",
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a -c]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua" --verbose]],
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\actions\\\\build\\\\cleaner.lua" --verbose]],
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\armeabi-v7a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a armeabi-v7a]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake clean -v",
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a -c]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\armeabi-v7a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a armeabi-v7a]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\armeabi-v7a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a armeabi-v7a]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\armeabi-v7a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a armeabi-v7a]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\armeabi-v7a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a armeabi-v7a]],
        [[xmake f -c -y -v -p android -a arm64-v8a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviFixMaybe\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviFixMaybe\app\build\xmake\install_artifacts.lua -o D:\LeviFixMaybe\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviFixMaybe\app\build\xmake]],
        "xmake build -v"
    }
}