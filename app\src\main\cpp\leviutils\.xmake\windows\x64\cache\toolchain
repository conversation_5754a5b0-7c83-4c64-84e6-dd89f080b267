{
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        }
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        plat = "android",
        arch = "armeabi-v7a",
        __global = true
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        plat = "android",
        arch = "armeabi-v7a",
        __global = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        }
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        arch = "armeabi-v7a",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        __checked = true,
        ndk_sdkver = "21",
        __global = true,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndkver = 25,
        plat = "android",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        cross = "arm-linux-androideabi-"
    }
}