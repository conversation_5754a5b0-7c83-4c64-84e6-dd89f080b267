{
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        cross = "arm-linux-androideabi-",
        __global = true,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        arch = "armeabi-v7a",
        ndkver = 25,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        plat = "android",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sdkver = "21",
        __checked = true,
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __global = true,
        __checked = true,
        plat = "android"
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a",
            plat = "android"
        }
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __global = true,
        __checked = true,
        plat = "android"
    }
}