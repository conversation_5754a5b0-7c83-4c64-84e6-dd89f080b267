2025-07-31 00:11:17.671 19048-19048 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-31 00:11:17.674 19048-19048 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@2f3909d
2025-07-31 00:11:17.811 19048-19048 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-31 00:11:17.823 19048-19222 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-31 00:11:17.823 19048-19222 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:11:17.823 19048-19222 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:11:17.834 19048-19048 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-31 00:11:17.881 19048-19048 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@5e3fe5b
2025-07-31 00:11:17.882 19048-19048 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@270d236
2025-07-31 00:11:17.882 19048-19048 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{5da2537 V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:305 
2025-07-31 00:11:17.889 19048-19048 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:11:17.891 19048-19063 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-31 00:11:17.905 19048-19048 InputTransport          org.levimc.launcher                  D  Input channel constructed: '18245f5', fd=149
2025-07-31 00:11:17.907 19048-19048 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:11:17.907 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:11:17.907 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@5da2537 IsHRR=false TM=true
2025-07-31 00:11:17.910 19048-19222 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-31 00:11:17.920 19048-19048 BufferQueueConsumer     org.levimc.launcher                  D  [](id:4a6800000002,api:0,p:-1,c:19048) connect: controlledByApp=false
2025-07-31 00:11:17.920 19048-19048 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@4c2caa4#2](f:0,a:0,s:0) constructor()
2025-07-31 00:11:17.921 19048-19048 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@4c2caa4 mNativeObject= 0xb400007988107400 sc.mNativeObject= 0xb400007989185440 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:11:17.921 19048-19048 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@4c2caa4 mNativeObject= 0xb400007988107400 sc.mNativeObject= 0xb400007989185440 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:11:17.922 19048-19048 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@4c2caa4#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-31 00:11:17.922 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=9 res=0x3 s={true 0xb400007989215000} ch=true seqId=0
2025-07-31 00:11:17.923 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:11:17.924 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007989215000} hwInitialized=true
2025-07-31 00:11:17.926 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:11:17.926 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@4c2caa4#6
2025-07-31 00:11:17.926 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@4c2caa4#7
2025-07-31 00:11:17.927 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-31 00:11:17.930 19048-19085 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-31 00:11:17.930 19048-19085 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  mWNT: t=0xb4000079890dd600 mBlastBufferQueue=0xb400007988107400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-31 00:11:17.930 19048-19085 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-31 00:11:17.936 19048-19063 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@4c2caa4#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-31 00:11:17.936 19048-19063 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@4c2caa4#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=312464241751008(auto) mPendingTransactions.size=0 graphicBufferId=81810537054238 transform=7
2025-07-31 00:11:17.937 19048-19063 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-31 00:11:17.938 19048-19063 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 19048    Tid : 19063
2025-07-31 00:11:17.938 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:11:17.942 19048-19063 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-31 00:11:17.952 19048-19048 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:11:17.953 19048-19048 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:11:17.973 19048-19222 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-31 00:11:17.987 19048-19048 VRI[MainAc...y]@4c2caa4 org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb400007989215000}
2025-07-31 00:11:17.987 19048-19048 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:11:17.988 19048-19048 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:11:17.995 19048-19060 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=150
2025-07-31 00:11:18.016 19048-19048 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-31 00:11:18.034 19048-19222 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-31 00:11:18.084 19048-19222 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-31 00:11:18.086 19048-19222 linker                  org.levimc.launcher                  E  library "/system/lib64/libc++_shared.so" ("/system/lib64/libc++_shared.so") needed or dlopened by "/apex/com.android.art/lib64/libnativeloader.so" is not accessible for the namespace: [name="clns-7", ld_library_paths="", default_library_paths="/data/app/~~8WfphTWdF8cDQGlyhTsIxw==/org.levimc.launcher-qLGwKSJeDfMVl8-o5kSNtQ==/lib/arm64:/data/app/~~8WfphTWdF8cDQGlyhTsIxw==/org.levimc.launcher-qLGwKSJeDfMVl8-o5kSNtQ==/base.apk!/lib/arm64-v8a", permitted_paths="/data:/mnt/expand:/data/data/org.levimc.launcher"]
2025-07-31 00:11:18.088 19048-19222 nativeloader            org.levimc.launcher                  D  Load /system/lib64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~8WfphTWdF8cDQGlyhTsIxw==/org.levimc.launcher-qLGwKSJeDfMVl8-o5kSNtQ==/base.apk!classes2.dex): dlopen failed: library "/system/lib64/libc++_shared.so" needed or dlopened by "/apex/com.android.art/lib64/libnativeloader.so" is not accessible for the namespace "clns-7"
2025-07-31 00:11:18.090 19048-19222 AndroidRuntime          org.levimc.launcher                  E  FATAL EXCEPTION: Thread-6
                                                                                                    Process: org.levimc.launcher, PID: 19048
                                                                                                    java.lang.UnsatisfiedLinkError: dlopen failed: library "/system/lib64/libc++_shared.so" needed or dlopened by "/apex/com.android.art/lib64/libnativeloader.so" is not accessible for the namespace "clns-7"
                                                                                                    	at java.lang.Runtime.loadLibrary0(Runtime.java:1090)
                                                                                                    	at java.lang.Runtime.loadLibrary0(Runtime.java:1012)
                                                                                                    	at java.lang.System.loadLibrary(System.java:1765)
                                                                                                    	at com.mojang.minecraftpe.Launcher.<clinit>(Launcher.java:38)
                                                                                                    	at java.lang.Class.classForName(Native Method)
                                                                                                    	at java.lang.Class.forName(Class.java:597)
                                                                                                    	at java.lang.Class.forName(Class.java:502)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.assertLauncherClassExists(MinecraftLauncher.java:148)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.launch(MinecraftLauncher.java:116)
                                                                                                    	at org.levimc.launcher.ui.activities.MainActivity.lambda$launchGame$17(MainActivity.java:334)
                                                                                                    	at org.levimc.launcher.ui.activities.MainActivity.$r8$lambda$VavTbLigfu9obLu-ZbiOvqZvWEw(Unknown Source:0)
                                                                                                    	at org.levimc.launcher.ui.activities.MainActivity$$ExternalSyntheticLambda24.run(D8$$SyntheticClass:0)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
2025-07-31 00:11:18.136 19048-19048 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:11:18.137 19048-19048 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
---------------------------- PROCESS ENDED (19048) for package org.levimc.launcher ----------------------------
2025-07-31 00:11:18.382  1598-1804  WindowManager           system_server                        E  win=Window{18245f5 u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=0 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda1.run:28 com.android.server.wm.SurfaceAnimator$$ExternalSyntheticLambda0.onAnimationFinished:65 com.android.server.wm.LocalAnimationAdapter$$ExternalSyntheticLambda0.run:10 android.os.Handler.handleCallback:959 
