2025-07-31 00:20:19.147 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-31 00:20:19.151 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@2f3909d
2025-07-31 00:20:19.162 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-31 00:20:19.167 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-31 00:20:19.175 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-31 00:20:19.177 23088-23166 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-31 00:20:19.177 23088-23166 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:20:19.177 23088-23166 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:20:19.195 23088-23088 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-31 00:20:19.226 23088-23088 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@3c1755e
2025-07-31 00:20:19.227 23088-23088 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@fe32b55
2025-07-31 00:20:19.227 23088-23088 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{3ab896a V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:305 
2025-07-31 00:20:19.230 23088-23088 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:20:19.231 23088-23108 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-31 00:20:19.242 23088-23088 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'cbb7e1f', fd=137
2025-07-31 00:20:19.243 23088-23088 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:20:19.245 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:20:19.245 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@3ab896a IsHRR=false TM=true
2025-07-31 00:20:19.253 23088-23166 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-31 00:20:19.260 23088-23088 BufferQueueConsumer     org.levimc.launcher                  D  [](id:5a3000000002,api:0,p:-1,c:23088) connect: controlledByApp=false
2025-07-31 00:20:19.261 23088-23088 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:0,s:0) constructor()
2025-07-31 00:20:19.261 23088-23088 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@5e3fe5b mNativeObject= 0xb4000079e598ac00 sc.mNativeObject= 0xb4000079888701c0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:20:19.261 23088-23088 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@5e3fe5b mNativeObject= 0xb4000079e598ac00 sc.mNativeObject= 0xb4000079888701c0 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:20:19.261 23088-23088 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-31 00:20:19.261 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=8 res=0x3 s={true 0xb4000079e99f6800} ch=true seqId=0
2025-07-31 00:20:19.262 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:20:19.263 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000079e99f6800} hwInitialized=true
2025-07-31 00:20:19.265 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:20:19.265 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@5e3fe5b#6
2025-07-31 00:20:19.265 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@5e3fe5b#7
2025-07-31 00:20:19.266 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-31 00:20:19.268 23088-23132 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-31 00:20:19.268 23088-23132 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  mWNT: t=0xb4000079887d0c00 mBlastBufferQueue=0xb4000079e598ac00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-31 00:20:19.269 23088-23132 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-31 00:20:19.271 23088-23108 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-31 00:20:19.271 23088-23108 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=313004184982733(auto) mPendingTransactions.size=0 graphicBufferId=99162204930075 transform=7
2025-07-31 00:20:19.272 23088-23108 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-31 00:20:19.274 23088-23108 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 23088    Tid : 23108
2025-07-31 00:20:19.274 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:20:19.280 23088-23108 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-31 00:20:19.290 23088-23088 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:20:19.291 23088-23088 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:20:19.307 23088-23166 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-31 00:20:19.325 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000079e99f6800}
2025-07-31 00:20:19.326 23088-23088 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:20:19.326 23088-23088 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:20:19.330 23088-23103 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=153
2025-07-31 00:20:19.339 23088-23088 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-31 00:20:19.353 23088-23166 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-31 00:20:19.399 23088-23166 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-31 00:20:19.399 23088-23166 LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-31 00:20:19.399 23088-23166 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:20:19.400 23088-23166 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:20:19.400 23088-23166 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:20:19.400 23088-23166 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:20:19.401 23088-23169 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully loaded Launcher class: com.mojang.minecraftpe.Launcher
2025-07-31 00:20:19.401 23088-23169 LeviLogger              org.levimc.launcher                  I  [LeviMC] Set MC_SRC to: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/base.apk
2025-07-31 00:20:19.401 23088-23169 LeviLogger              org.levimc.launcher                  I  [LeviMC] Set MC_SPLIT_SRC with 11 entries
2025-07-31 00:20:19.404 23088-23169 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully loaded SO mods
2025-07-31 00:20:19.404 23088-23169 LeviLogger              org.levimc.launcher                  I  [LeviMC] About to start minecraft activity with intent: Intent { flg=0x14000000 cmp=org.levimc.launcher/com.mojang.minecraftpe.Launcher (has extras) }
2025-07-31 00:20:19.404 23088-23088 LeviLogger              org.levimc.launcher                  I  [LeviMC] Starting minecraft activity from UI thread
2025-07-31 00:20:19.437 23088-23088 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully called startActivity
2025-07-31 00:20:19.460 23088-23088 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-31 00:20:19.468 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Minecraft Launcher onCreate called
2025-07-31 00:20:19.468 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Loading native libraries
2025-07-31 00:20:19.472 23088-23088 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-31 00:20:19.472 23088-23088 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~AdPwnC3CdDtaAj2yX4qcIg==/org.levimc.launcher-Ssw-pu_Bbjmbuq9n8UUeyA==/base.apk!/lib/arm64-v8a/libpreloader.so
2025-07-31 00:20:19.472 23088-23088 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-31 00:20:19.474 23088-23088 nativeloader            org.levimc.launcher                  D  Load /data/app/~~AdPwnC3CdDtaAj2yX4qcIg==/org.levimc.launcher-Ssw-pu_Bbjmbuq9n8UUeyA==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=/data/app/~~AdPwnC3CdDtaAj2yX4qcIg==/org.levimc.launcher-Ssw-pu_Bbjmbuq9n8UUeyA==/base.apk!classes2.dex): ok
2025-07-31 00:20:19.478 23088-23088 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~AdPwnC3CdDtaAj2yX4qcIg==/org.levimc.launcher-Ssw-pu_Bbjmbuq9n8UUeyA==/base.apk!classes2.dex): ok
2025-07-31 00:20:19.480 23088-23088 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~AdPwnC3CdDtaAj2yX4qcIg==/org.levimc.launcher-Ssw-pu_Bbjmbuq9n8UUeyA==/base.apk!classes2.dex): ok
2025-07-31 00:20:19.584 23088-23088 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~AdPwnC3CdDtaAj2yX4qcIg==/org.levimc.launcher-Ssw-pu_Bbjmbuq9n8UUeyA==/base.apk!classes2.dex): ok
2025-07-31 00:20:19.584 23088-23088 Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x7adf10ad28
2025-07-31 00:20:19.584 23088-23088 Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-31 00:20:19.584 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  MC_PATH: 
2025-07-31 00:20:19.584 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  MC_SRC: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/base.apk
2025-07-31 00:20:19.584 23088-23088 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lcom/mojang/minecraftpe/Launcher; (domain=app) using reflection: allowed
2025-07-31 00:20:19.585 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Processing 11 split sources
2025-07-31 00:20:19.585 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.arm64_v8a.apk
2025-07-31 00:20:19.586 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.bn.apk
2025-07-31 00:20:19.587 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.en.apk
2025-07-31 00:20:19.587 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.gu.apk
2025-07-31 00:20:19.587 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.hi.apk
2025-07-31 00:20:19.588 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.kn.apk
2025-07-31 00:20:19.588 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.mr.apk
2025-07-31 00:20:19.589 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.ta.apk
2025-07-31 00:20:19.589 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.te.apk
2025-07-31 00:20:19.590 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.xxhdpi.apk
2025-07-31 00:20:19.590 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Adding split source: /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_install_pack.apk
2025-07-31 00:20:19.590 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Calling super.onCreate
2025-07-31 00:20:19.593 23088-23088 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@942d71f
2025-07-31 00:20:19.598 23088-23088 nativeloader            org.levimc.launcher                  D  Load /data/app/~~AdPwnC3CdDtaAj2yX4qcIg==/org.levimc.launcher-Ssw-pu_Bbjmbuq9n8UUeyA==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=<unknown>): ok
2025-07-31 00:20:19.599 23088-23088 MinecraftLauncher       org.levimc.launcher                  I  Minecraft Launcher onCreate completed successfully
2025-07-31 00:20:19.602 23088-23088 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:20:19.602 23088-23088 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars captionBar, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-31 00:20:19.607 23088-23088 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'bc26dfc', fd=174
2025-07-31 00:20:19.608 23088-23088 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:20:19.608 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:20:19.608 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@6e1e2b1 IsHRR=false TM=true
2025-07-31 00:20:19.609 23088-23088 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:20:19.609 23088-23088 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:20:19.624 23088-23088 BufferQueueConsumer     org.levimc.launcher                  D  [](id:5a3000000003,api:0,p:-1,c:23088) connect: controlledByApp=false
2025-07-31 00:20:19.626 23088-23088 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@36404ca#3](f:0,a:0,s:0) constructor()
2025-07-31 00:20:19.626 23088-23088 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[Launcher]@36404ca mNativeObject= 0xb400007a220f3000 sc.mNativeObject= 0xb4000079888dcdc0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:20:19.626 23088-23088 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@36404ca mNativeObject= 0xb400007a220f3000 sc.mNativeObject= 0xb4000079888dcdc0 format= 4 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:20:19.627 23088-23088 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@36404ca#3](f:0,a:0,s:0) update width=2340 height=1080 format=4 mTransformHint=4
2025-07-31 00:20:19.627 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=14 res=0x3 s={true 0xb40000798882d800} ch=true seqId=0
2025-07-31 00:20:19.628 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:20:19.628 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  ViewRootImpl >> surfaceCreated
2025-07-31 00:20:19.628 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  ViewRootImpl >> surfaceChanged W=2340, H=1080)
2025-07-31 00:20:19.628 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-31 00:20:19.629 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:20:19.629 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-31 00:20:19.629 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@36404ca#8
2025-07-31 00:20:19.629 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@36404ca#9
2025-07-31 00:20:19.630 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:20:19.633 23088-23088 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:20:19.633 23088-23088 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=true, type=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-31 00:20:19.634 23088-23088 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=statusBars, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-31 00:20:19.636 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-31 00:20:19.636 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-31 00:20:19.636 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-31 00:20:19.637 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@36404ca#10
2025-07-31 00:20:19.637 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@36404ca#11
2025-07-31 00:20:19.637 23088-23088 VRI[Launcher]@36404ca   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:20:19.660 23088-23088 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:20:19.660 23088-23088 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:20:19.664 23088-23100 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=151
2025-07-31 00:20:19.676 23088-23088 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-31 00:20:19.972 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-31 00:20:19.972 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-31 00:20:19.995 23088-23088 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:3,s:0) destructor()
2025-07-31 00:20:19.995 23088-23088 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@5e3fe5b#2(BLAST Consumer)2](id:5a3000000002,api:0,p:-1,c:23088) disconnect
2025-07-31 00:20:19.996 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Relayout returned: old=(982,335,1358,745) new=(982,335,1358,745) relayoutAsync=false req=(376,410)8 dur=7 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-31 00:20:19.996 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-31 00:20:19.996 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-31 00:20:19.997 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-31 00:20:19.998 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  stopped(true) old = false
2025-07-31 00:20:19.998 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-31 00:20:20.005 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  stopped(true) old = false
2025-07-31 00:20:20.005 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-31 00:20:20.006 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:20.013 23088-23088 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@4d31d0c
2025-07-31 00:20:20.018 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:20.019 23088-23088 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-31 00:20:20.024 23088-23088 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'f6c9361', fd=206
2025-07-31 00:20:20.027 23088-23100 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:3,s:0) destructor()
2025-07-31 00:20:20.027 23088-23100 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@2f3909d#1(BLAST Consumer)1](id:5a3000000001,api:0,p:-1,c:23088) disconnect
2025-07-31 00:20:20.028 23088-23088 WindowManager           org.levimc.launcher                  E  android.view.WindowLeaked: Activity org.levimc.launcher.ui.activities.MainActivity has leaked window com.android.internal.policy.DecorView{3ab896a V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity] that was originally added here
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1527)
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1502)
                                                                                                    	at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:544)
                                                                                                    	at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
                                                                                                    	at android.app.Dialog.show(Dialog.java:511)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading(MinecraftLauncher.java:305)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher$$ExternalSyntheticLambda4.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-31 00:20:20.029 23088-23088 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{3ab896a V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity], caller=android.view.WindowManagerGlobal.closeAllExceptView:672 android.view.WindowManagerGlobal.closeAll:644 android.app.ActivityThread.handleDestroyActivity:6747 
2025-07-31 00:20:20.030 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:20.032 23088-23088 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=statusBars, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 
2025-07-31 00:20:20.032 23088-23088 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@270d236
2025-07-31 00:20:20.033 23088-23088 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-31 00:20:20.037  1598-1804  WindowManager           system_server                        E  win=Window{cbb7e1f u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator.cancelAnimation:19 com.android.server.wm.SurfaceAnimator.cancelAnimation:1 com.android.server.wm.WindowContainer.setParent:49 com.android.server.wm.WindowContainer.removeChild:17 
2025-07-31 00:20:20.040 23088-23088 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'cbb7e1f', fd=137
2025-07-31 00:20:37.501 23088-23094 levimc.launcher         org.levimc.launcher                  W  Cleared Reference was only reachable from finalizer (only reported once)
2025-07-31 00:20:37.518 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:37.519 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:37.522 23088-23095 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=232
2025-07-31 00:20:37.524 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:37.525 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:37.528 23088-23095 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=153
2025-07-31 00:20:37.529 23088-23095 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=151
2025-07-31 00:20:37.531 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:37.533 23088-23108 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:20:37.535 23088-23096 System                  org.levimc.launcher                  W  A resource failed to call ZipFile.close. 