---------------------------- PROCESS STARTED (22768) for package org.levimc.launcher ----------------------------
2025-07-31 00:17:26.940 22768-22768 nativeloader            org.levimc.launcher                  D  Configuring clns-7 for other apk /data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/lib/arm64:/data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/org.levimc.launcher
2025-07-31 00:17:26.943 22768-22768 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 202956589; UID 10647; state: ENABLED
2025-07-31 00:17:26.963 22768-22768 GraphicsEnvironment     org.levimc.launcher                  V  Currently set values for:
2025-07-31 00:17:26.963 22768-22768 GraphicsEnvironment     org.levimc.launcher                  V    angle_gl_driver_selection_pkgs=[]
2025-07-31 00:17:26.963 22768-22768 GraphicsEnvironment     org.levimc.launcher                  V    angle_gl_driver_selection_values=[]
2025-07-31 00:17:26.963 22768-22768 GraphicsEnvironment     org.levimc.launcher                  V  Global.Settings values are invalid: number of packages: 0, number of values: 0
2025-07-31 00:17:26.964 22768-22768 GraphicsEnvironment     org.levimc.launcher                  V  Neither updatable production driver nor prerelease driver is supported.
2025-07-31 00:17:27.055 22768-22768 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 279646685; UID 10647; state: ENABLED
2025-07-31 00:17:27.103 22768-22768 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-31 00:17:27.109 22768-22768 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-31 00:17:27.143 22768-22768 AppCompatDelegate       org.levimc.launcher                  D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
2025-07-31 00:17:27.147 22768-22789 HWUI                    org.levimc.launcher                  D  CacheManager constructor. deviceInfo=(1080, 1920)
2025-07-31 00:17:27.148 22768-22789 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb4000079ee4a5980
2025-07-31 00:17:27.149 22768-22789 libMEOW                 org.levimc.launcher                  D  meow reload base cfg path: na
2025-07-31 00:17:27.149 22768-22789 libMEOW                 org.levimc.launcher                  D  meow reload overlay cfg path: na
2025-07-31 00:17:27.157 22768-22789 QT                      org.levimc.launcher                  W  qt_process_init() called
2025-07-31 00:17:27.157 22768-22789 QT                      org.levimc.launcher                  E  [QT]file does not exist
2025-07-31 00:17:27.157 22768-22789 QT                      org.levimc.launcher                  W  Support!!
2025-07-31 00:17:27.163 22768-22789 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-31 00:17:27.206 22768-22768 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 309578419; UID 10647; state: ENABLED
2025-07-31 00:17:27.219 22768-22768 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@5d8f5e9
2025-07-31 00:17:27.228 22768-22768 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-31 00:17:27.297 22768-22768 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 63938206; UID 10647; state: ENABLED
2025-07-31 00:17:27.323 22768-22768 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 352594277; UID 10647; state: ENABLED
2025-07-31 00:17:27.328 22768-22768 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:17:27.329 22768-22768 HardwareRenderer        org.levimc.launcher                  D  onDisplayChanged. displayId=0 current wxh=2340x1080 mLargest wxh=0x0
2025-07-31 00:17:27.329 22768-22768 HWUI                    org.levimc.launcher                  W  Unknown dataspace 0
2025-07-31 00:17:27.329 22768-22768 HardwareRenderer        org.levimc.launcher                  D  Set largestWidth and largestHeight as logical resolution. (2340x1080)
2025-07-31 00:17:27.329 22768-22789 HWUI                    org.levimc.launcher                  D  setMaxSurfaceArea requested wxh=(2340,1080) requestedSurfaceArea(2527200) mMaxSurfaceArea(2073600)
2025-07-31 00:17:27.330 22768-22789 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-31 00:17:27.333 22768-22768 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars navigationBars captionBar, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-31 00:17:27.338 22768-22768 InputTransport          org.levimc.launcher                  D  Input channel constructed: '2ae8d05', fd=131
2025-07-31 00:17:27.339 22768-22768 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:17:27.341 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:17:27.342 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@8b54e8a IsHRR=false TM=true
2025-07-31 00:17:27.342 22768-22768 IDS_TAG                 org.levimc.launcher                  I  Starting IDS observe window
2025-07-31 00:17:27.343 22768-22768 IDS_TAG                 org.levimc.launcher                  I  Getting Shared Preference for android.app.Application@b3cbf0d uid = 10647
2025-07-31 00:17:27.347 22768-22768 IDS_TAG                 org.levimc.launcher                  I  App android.app.Application@b3cbf0d has not finished training
2025-07-31 00:17:27.348 22768-22789 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-31 00:17:27.348 22768-22768 IDS_TAG                 org.levimc.launcher                  I  Closing IDS observe window
2025-07-31 00:17:27.348 22768-22768 IDS_TAG                 org.levimc.launcher                  I  Getting Shared Preference for android.app.Application@b3cbf0d uid = 10647
2025-07-31 00:17:27.348 22768-22768 IDS_TAG                 org.levimc.launcher                  I  IDS count updated to 2 for android.app.Application@b3cbf0d
2025-07-31 00:17:27.372 22768-22768 BufferQueueConsumer     org.levimc.launcher                  D  [](id:58f000000000,api:0,p:-1,c:22768) connect: controlledByApp=false
2025-07-31 00:17:27.377 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@913287e#0](f:0,a:0,s:0) constructor()
2025-07-31 00:17:27.377 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[SplashActivity]@913287e mNativeObject= 0xb400007988ff5800 sc.mNativeObject= 0xb4000079e5810ac0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:17:27.377 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[SplashActivity]@913287e mNativeObject= 0xb400007988ff5800 sc.mNativeObject= 0xb4000079e5810ac0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:17:27.378 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@913287e#0](f:0,a:0,s:0) update width=2340 height=1080 format=-1 mTransformHint=4
2025-07-31 00:17:27.379 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=13 res=0x3 s={true 0xb4000079e59b0000} ch=true seqId=0
2025-07-31 00:17:27.379 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:17:27.381 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000079e59b0000} hwInitialized=true
2025-07-31 00:17:27.386 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:17:27.386 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[SplashActivity]@913287e#0
2025-07-31 00:17:27.386 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  Creating new active sync group VRI[SplashActivity]@913287e#1
2025-07-31 00:17:27.390 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-31 00:17:27.394 22768-22809 VRI[Splash...y]@913287e org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-31 00:17:27.395 22768-22809 VRI[Splash...y]@913287e org.levimc.launcher                  I  mWNT: t=0xb400007988fc9680 mBlastBufferQueue=0xb400007988ff5800 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-31 00:17:27.395 22768-22809 VRI[Splash...y]@913287e org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-31 00:17:27.397 22768-22789 levimc.launcher         org.levimc.launcher                  D  Can't load libmbrainSDK
2025-07-31 00:17:27.408 22768-22789 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@913287e#0](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-31 00:17:27.408 22768-22789 GrallocExtra            org.levimc.launcher                  I  gralloc_extra_query:is_SW3D 0
2025-07-31 00:17:27.409 22768-22789 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@913287e#0](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=312832322427338(auto) mPendingTransactions.size=0 graphicBufferId=97787815395332 transform=7
2025-07-31 00:17:27.409 22768-22789 levimc.launcher         org.levimc.launcher                  D  Can't load libmbrainSDK
2025-07-31 00:17:27.410 22768-22789 VRI[Splash...y]@913287e org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-31 00:17:27.411 22768-22789 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 22768    Tid : 22789
2025-07-31 00:17:27.411 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:17:27.414 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  onDisplayChanged oldDisplayState=2 newDisplayState=2
2025-07-31 00:17:27.415 22768-22768 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:17:27.417 22768-22768 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=statusBars navigationBars, animType=1, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-31 00:17:27.418 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-31 00:17:27.418 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-31 00:17:27.418 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-31 00:17:27.418 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[SplashActivity]@913287e#2
2025-07-31 00:17:27.418 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  Creating new active sync group VRI[SplashActivity]@913287e#3
2025-07-31 00:17:27.419 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-31 00:17:27.422 22768-22809 VRI[Splash...y]@913287e org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-31 00:17:27.422 22768-22809 VRI[Splash...y]@913287e org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-31 00:17:27.435 22768-22789 VRI[Splash...y]@913287e org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-31 00:17:27.435 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:17:27.469 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000079e59b0000}
2025-07-31 00:17:27.470 22768-22768 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:17:27.470 22768-22768 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:17:27.479 22768-22780 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=180
2025-07-31 00:17:27.502 22768-22768 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:17:27.503 22768-22768 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity
2025-07-31 00:17:27.695 22768-22772 levimc.launcher         org.levimc.launcher                  I  Compiler allocated 4280KB to compile void android.widget.TextView.<init>(android.content.Context, android.util.AttributeSet, int, int)
2025-07-31 00:17:27.791 22768-22812 InteractionJankMonitor  org.levimc.launcher                  W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=org.levimc.launcher
2025-07-31 00:17:27.803 22768-22768 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=statusBars navigationBars, animType=1, host=org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 
2025-07-31 00:17:27.883 22768-22772 levimc.launcher         org.levimc.launcher                  I  Compiler allocated 7416KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-31 00:17:29.419 22768-22768 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-31 00:17:29.444 22768-22768 nativeloader            org.levimc.launcher                  D  Load /data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!/lib/arm64-v8a/libleviutils.so using class loader ns clns-7 (caller=/data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!classes12.dex): ok
2025-07-31 00:17:29.453 22768-22768 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@28b1843
2025-07-31 00:17:29.781 22768-22768 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-31 00:17:29.835 22768-22768 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:17:29.836 22768-22789 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-31 00:17:29.837 22768-22768 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars navigationBars captionBar, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-31 00:17:29.842 22768-22768 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'dc49f5 ', fd=206
2025-07-31 00:17:29.843 22768-22768 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:17:29.843 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:17:29.843 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@815b2e0 IsHRR=false TM=true
2025-07-31 00:17:29.852 22768-22789 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-31 00:17:29.880 22768-22768 BufferQueueConsumer     org.levimc.launcher                  D  [](id:58f000000001,api:0,p:-1,c:22768) connect: controlledByApp=false
2025-07-31 00:17:29.880 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:0,s:0) constructor()
2025-07-31 00:17:29.880 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@2f3909d mNativeObject= 0xb400007989214400 sc.mNativeObject= 0xb4000079891ceec0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:17:29.880 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[MainActivity]@2f3909d mNativeObject= 0xb400007989214400 sc.mNativeObject= 0xb4000079891ceec0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:17:29.880 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:0,s:0) update width=2340 height=1080 format=-1 mTransformHint=4
2025-07-31 00:17:29.881 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=16 res=0x3 s={true 0xb40000798920a000} ch=true seqId=0
2025-07-31 00:17:29.881 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:17:29.882 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb40000798920a000} hwInitialized=true
2025-07-31 00:17:29.885 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:17:29.885 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@2f3909d#4
2025-07-31 00:17:29.885 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@2f3909d#5
2025-07-31 00:17:29.886 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-31 00:17:29.897 22768-22809 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-31 00:17:29.898 22768-22809 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  mWNT: t=0xb4000079891a5400 mBlastBufferQueue=0xb400007989214400 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-31 00:17:29.898 22768-22809 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-31 00:17:29.904 22768-22789 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-31 00:17:29.905 22768-22789 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=312834818197492(auto) mPendingTransactions.size=0 graphicBufferId=97787815395346 transform=7
2025-07-31 00:17:29.905 22768-22789 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-31 00:17:29.907 22768-22789 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 22768    Tid : 22789
2025-07-31 00:17:29.907 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:17:29.908 22768-22768 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:17:29.908 22768-22768 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:17:29.916 22768-22768 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=navigationBars, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-31 00:17:29.917 22768-22768 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=statusBars, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-31 00:17:29.968 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb40000798920a000}
2025-07-31 00:17:29.969 22768-22768 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:17:29.969 22768-22768 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:17:29.977 22768-22783 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=232
2025-07-31 00:17:30.001 22768-22768 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-31 00:17:30.444 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-31 00:17:30.445 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-31 00:17:30.471 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[SplashActivity]@913287e#0](f:0,a:3,s:0) destructor()
2025-07-31 00:17:30.471 22768-22768 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[SplashActivity]@913287e#0(BLAST Consumer)0](id:58f000000000,api:0,p:-1,c:22768) disconnect
2025-07-31 00:17:30.473 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)8 dur=10 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-31 00:17:30.474 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-31 00:17:30.478 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  stopped(true) old = false
2025-07-31 00:17:30.478 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.SplashActivity set to true
2025-07-31 00:17:30.487 22768-22768 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@340a9ad
2025-07-31 00:17:30.490 22768-22768 VRI[Splash...y]@913287e org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-31 00:17:30.493 22768-22768 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '2ae8d05', fd=131
2025-07-31 00:17:30.510 22768-22824 LeviLogger              org.levimc.launcher                  E  [LeviMC] Parse error: For input string: "unknown"
2025-07-31 00:17:32.075 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-31 00:17:32.080 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@2f3909d
2025-07-31 00:17:32.195 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-31 00:17:32.216 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-31 00:17:32.218 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes2.dex
2025-07-31 00:17:32.220 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] Deleted: classes.dex
2025-07-31 00:17:32.220 22768-22840 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:17:32.220 22768-22840 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:17:32.231 22768-22768 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-31 00:17:32.259 22768-22768 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@e64ca12
2025-07-31 00:17:32.259 22768-22768 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@3763e99
2025-07-31 00:17:32.260 22768-22768 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{3c1755e V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:305 
2025-07-31 00:17:32.262 22768-22768 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:17:32.267 22768-22789 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-31 00:17:32.275 22768-22768 InputTransport          org.levimc.launcher                  D  Input channel constructed: '23c3cbf', fd=142
2025-07-31 00:17:32.276 22768-22768 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:17:32.276 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:17:32.276 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@3c1755e IsHRR=false TM=true
2025-07-31 00:17:32.279 22768-22841 ProfileInstaller        org.levimc.launcher                  D  Installing profile for org.levimc.launcher
2025-07-31 00:17:32.287 22768-22768 BufferQueueConsumer     org.levimc.launcher                  D  [](id:58f000000002,api:0,p:-1,c:22768) connect: controlledByApp=false
2025-07-31 00:17:32.287 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-31 00:17:32.287 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@fd5cd3f#2](f:0,a:0,s:0) constructor()
2025-07-31 00:17:32.288 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@fd5cd3f mNativeObject= 0xb400007a21fde000 sc.mNativeObject= 0xb400007988382700 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:17:32.288 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@fd5cd3f mNativeObject= 0xb400007a21fde000 sc.mNativeObject= 0xb400007988382700 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:17:32.288 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@fd5cd3f#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-31 00:17:32.289 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=7 res=0x3 s={true 0xb4000079e59b0000} ch=true seqId=0
2025-07-31 00:17:32.290 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:17:32.291 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000079e59b0000} hwInitialized=true
2025-07-31 00:17:32.292 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:17:32.292 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@fd5cd3f#6
2025-07-31 00:17:32.292 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@fd5cd3f#7
2025-07-31 00:17:32.292 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-31 00:17:32.294 22768-22808 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-31 00:17:32.294 22768-22808 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  mWNT: t=0xb4000079881f1880 mBlastBufferQueue=0xb400007a21fde000 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-31 00:17:32.294 22768-22808 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-31 00:17:32.297 22768-22789 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@fd5cd3f#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-31 00:17:32.297 22768-22789 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@fd5cd3f#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=312837211054492(auto) mPendingTransactions.size=0 graphicBufferId=97787815395352 transform=7
2025-07-31 00:17:32.297 22768-22789 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-31 00:17:32.299 22768-22789 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 22768    Tid : 22789
2025-07-31 00:17:32.299 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:17:32.303 22768-22789 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-31 00:17:32.313 22768-22768 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:17:32.313 22768-22768 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:17:32.344 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-31 00:17:32.349 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000079e59b0000}
2025-07-31 00:17:32.349 22768-22768 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:17:32.349 22768-22768 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:17:32.353 22768-22816 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=151
2025-07-31 00:17:32.381 22768-22768 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-31 00:17:32.387 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-31 00:17:32.434 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-31 00:17:32.434 22768-22840 LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-31 00:17:32.434 22768-22840 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:17:32.434 22768-22840 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:17:32.434 22768-22840 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:17:32.434 22768-22840 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:17:32.476 22768-22768 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-31 00:17:32.487 22768-22768 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-31 00:17:32.487 22768-22768 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!/lib/arm64-v8a/libpreloader.so
2025-07-31 00:17:32.487 22768-22768 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-31 00:17:32.491 22768-22768 nativeloader            org.levimc.launcher                  D  Load /data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=/data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!classes2.dex): ok
2025-07-31 00:17:32.495 22768-22768 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!classes2.dex): ok
2025-07-31 00:17:32.497 22768-22768 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!classes2.dex): ok
2025-07-31 00:17:32.607 22768-22768 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!classes2.dex): ok
2025-07-31 00:17:32.607 22768-22768 Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x7adf10ad28
2025-07-31 00:17:32.607 22768-22768 Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-31 00:17:32.608 22768-22768 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lcom/mojang/minecraftpe/Launcher; (domain=app) using reflection: allowed
2025-07-31 00:17:32.615 22768-22768 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@222fd79
2025-07-31 00:17:32.619 22768-22768 nativeloader            org.levimc.launcher                  D  Load /data/app/~~oo_YkiJFRok-QaoF8039fQ==/org.levimc.launcher-snwza2J_hI0TV-gZB8KpEg==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=<unknown>): ok
2025-07-31 00:17:32.623 22768-22768 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:17:32.624 22768-22768 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars captionBar, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-31 00:17:32.628 22768-22768 InputTransport          org.levimc.launcher                  D  Input channel constructed: '5d4d58f', fd=261
2025-07-31 00:17:32.629 22768-22768 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:17:32.629 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:17:32.630 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@947c63b IsHRR=false TM=true
2025-07-31 00:17:32.646 22768-22768 BufferQueueConsumer     org.levimc.launcher                  D  [](id:58f000000003,api:0,p:-1,c:22768) connect: controlledByApp=false
2025-07-31 00:17:32.646 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@ffb3a6c#3](f:0,a:0,s:0) constructor()
2025-07-31 00:17:32.646 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[Launcher]@ffb3a6c mNativeObject= 0xb400007a21fdcc00 sc.mNativeObject= 0xb4000079884e43c0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:17:32.647 22768-22768 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@ffb3a6c mNativeObject= 0xb400007a21fdcc00 sc.mNativeObject= 0xb4000079884e43c0 format= 4 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:17:32.647 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@ffb3a6c#3](f:0,a:0,s:0) update width=2340 height=1080 format=4 mTransformHint=4
2025-07-31 00:17:32.647 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=13 res=0x3 s={true 0xb40000798844c000} ch=true seqId=0
2025-07-31 00:17:32.648 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:17:32.648 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  ViewRootImpl >> surfaceCreated
2025-07-31 00:17:32.648 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  ViewRootImpl >> surfaceChanged W=2340, H=1080)
2025-07-31 00:17:32.648 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-31 00:17:32.649 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:17:32.649 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-31 00:17:32.650 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@ffb3a6c#8
2025-07-31 00:17:32.650 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@ffb3a6c#9
2025-07-31 00:17:32.650 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:17:32.652 22768-22768 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:17:32.652 22768-22768 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:17:32.656 22768-22768 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:17:32.656 22768-22768 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=true, type=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-31 00:17:32.657 22768-22768 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=statusBars, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-31 00:17:32.658 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-31 00:17:32.658 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-31 00:17:32.658 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-31 00:17:32.658 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@ffb3a6c#10
2025-07-31 00:17:32.658 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@ffb3a6c#11
2025-07-31 00:17:32.658 22768-22768 VRI[Launcher]@ffb3a6c   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:17:32.681 22768-22768 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:17:32.682 22768-22768 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:17:32.685 22768-22815 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=150
2025-07-31 00:17:32.694 22768-22768 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-31 00:17:32.936 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-31 00:17:32.936 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-31 00:17:32.950 22768-22768 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@fd5cd3f#2](f:0,a:3,s:0) destructor()
2025-07-31 00:17:32.950 22768-22768 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@fd5cd3f#2(BLAST Consumer)2](id:58f000000002,api:0,p:-1,c:22768) disconnect
2025-07-31 00:17:32.951 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  Relayout returned: old=(982,335,1358,745) new=(982,335,1358,745) relayoutAsync=false req=(376,410)8 dur=9 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-31 00:17:32.951 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-31 00:17:32.951 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-31 00:17:32.951 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-31 00:17:32.952 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  stopped(true) old = false
2025-07-31 00:17:32.952 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-31 00:17:32.955 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  stopped(true) old = false
2025-07-31 00:17:32.955 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-31 00:17:32.955 22768-22789 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:17:32.960 22768-22768 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@4d31d0c
2025-07-31 00:17:32.962 22768-22789 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:17:32.962 22768-22768 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-31 00:17:32.964 22768-22816 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:3,s:0) destructor()
2025-07-31 00:17:32.964 22768-22816 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@2f3909d#1(BLAST Consumer)1](id:58f000000001,api:0,p:-1,c:22768) disconnect
2025-07-31 00:17:32.964 22768-22768 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'dc49f5 ', fd=206
2025-07-31 00:17:32.966 22768-22768 WindowManager           org.levimc.launcher                  E  android.view.WindowLeaked: Activity org.levimc.launcher.ui.activities.MainActivity has leaked window com.android.internal.policy.DecorView{3c1755e V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity] that was originally added here
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1527)
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1502)
                                                                                                    	at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:544)
                                                                                                    	at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
                                                                                                    	at android.app.Dialog.show(Dialog.java:511)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading(MinecraftLauncher.java:305)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher$$ExternalSyntheticLambda4.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-31 00:17:32.967 22768-22768 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{3c1755e V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity], caller=android.view.WindowManagerGlobal.closeAllExceptView:672 android.view.WindowManagerGlobal.closeAll:644 android.app.ActivityThread.handleDestroyActivity:6747 
2025-07-31 00:17:32.967 22768-22789 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:17:32.968 22768-22768 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@5e3fe5b
2025-07-31 00:17:32.970 22768-22768 VRI[MainAc...y]@fd5cd3f org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-31 00:17:32.971 22768-22768 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '23c3cbf', fd=142
2025-07-31 00:17:32.975  1598-1804  WindowManager           system_server                        E  win=Window{23c3cbf u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator.cancelAnimation:19 com.android.server.wm.SurfaceAnimator.cancelAnimation:1 com.android.server.wm.WindowContainer.setParent:49 com.android.server.wm.WindowContainer.removeChild:17 
2025-07-31 00:17:33.028 22768-22768 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=statusBars, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 