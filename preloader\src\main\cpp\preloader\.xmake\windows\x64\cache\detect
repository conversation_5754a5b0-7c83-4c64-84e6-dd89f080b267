{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--disable-tsaware"] = true,
            ["--no-whole-archive"] = true,
            ["--no-fatal-warnings"] = true,
            ["--nxcompat"] = true,
            ["--no-demangle"] = true,
            ["--shared"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-dy"] = true,
            ["-o"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-insert-timestamp"] = true,
            ["--Bstatic"] = true,
            ["--disable-auto-import"] = true,
            ["--high-entropy-va"] = true,
            ["--exclude-all-symbols"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--version"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["-m"] = true,
            ["--insert-timestamp"] = true,
            ["--gc-sections"] = true,
            ["--no-gc-sections"] = true,
            ["-l"] = true,
            ["-static"] = true,
            ["--disable-dynamicbase"] = true,
            ["-s"] = true,
            ["--dynamicbase"] = true,
            ["--strip-debug"] = true,
            ["--allow-multiple-definition"] = true,
            ["-S"] = true,
            ["--large-address-aware"] = true,
            ["--whole-archive"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--fatal-warnings"] = true,
            ["--Bdynamic"] = true,
            ["-L"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--disable-nxcompat"] = true,
            ["--tsaware"] = true,
            ["--help"] = true,
            ["--verbose"] = true,
            ["-v"] = true,
            ["--export-all-symbols"] = true,
            ["--demangle"] = true,
            ["--no-dynamicbase"] = true,
            ["--enable-auto-import"] = true,
            ["--kill-at"] = true,
            ["--appcontainer"] = true,
            ["-dn"] = true,
            ["--disable-no-seh"] = true,
            ["--no-seh"] = true,
            ["--strip-all"] = true
        }
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            license = "MIT",
            version = "v3.11.3",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            }
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            license = "MIT",
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            static = true,
            links = {
                "fmt"
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            version = "10.2.1",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            }
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-relocatable-pch"] = true,
            ["-dependency-file"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-faddrsig"] = true,
            ["-print-effective-triple"] = true,
            ["-cl-opt-disable"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-mno-madd4"] = true,
            ["-arch"] = true,
            ["-shared-libsan"] = true,
            ["-fconvergent-functions"] = true,
            ["-module-dependency-dir"] = true,
            ["-MJ"] = true,
            ["-gline-tables-only"] = true,
            ["-mcrc"] = true,
            ["-fborland-extensions"] = true,
            ["-fcommon"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-trigraphs"] = true,
            ["-ffixed-x16"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-ffixed-x15"] = true,
            ["-mnvs"] = true,
            ["-mno-global-merge"] = true,
            ["-I-"] = true,
            ["-z"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fstack-protector"] = true,
            ["-fdwarf-exceptions"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-femit-all-decls"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fignore-exceptions"] = true,
            ["-iquote"] = true,
            ["-moutline-atomics"] = true,
            ["-MQ"] = true,
            ["-flto"] = true,
            ["-ffixed-x26"] = true,
            ["-fms-extensions"] = true,
            ["-mlvi-hardening"] = true,
            ["-mseses"] = true,
            ["-mno-memops"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["--cuda-device-only"] = true,
            ["-freciprocal-math"] = true,
            ["-fno-builtin"] = true,
            ["-ffixed-a6"] = true,
            ["-fdebug-macro"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-serialize-diagnostics"] = true,
            ["-x"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-Xpreprocessor"] = true,
            ["-gmodules"] = true,
            ["-print-ivar-layout"] = true,
            ["-MP"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-iwithprefixbefore"] = true,
            ["-ivfsoverlay"] = true,
            ["-iwithprefix"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-ffixed-x11"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fprofile-generate"] = true,
            ["-cl-mad-enable"] = true,
            ["-mmsa"] = true,
            ["-miamcu"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fshort-wchar"] = true,
            ["-fsave-optimization-record"] = true,
            ["-imacros"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fpcc-struct-return"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-ffixed-x14"] = true,
            ["-static-libsan"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-mno-save-restore"] = true,
            ["-mfp32"] = true,
            ["-MD"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-operator-names"] = true,
            ["-fwritable-strings"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fgnu-runtime"] = true,
            ["-fno-digraphs"] = true,
            ["-fstandalone-debug"] = true,
            ["-fno-stack-protector"] = true,
            ["-MF"] = true,
            ["-mstackrealign"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-emit-module"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-integrated-as"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fmodules"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fstack-protector-all"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fno-common"] = true,
            ["-mno-gpopt"] = true,
            ["-print-targets"] = true,
            ["-moutline"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-help"] = true,
            ["-ftrapv"] = true,
            ["-fsized-deallocation"] = true,
            ["-ffixed-x28"] = true,
            ["-S"] = true,
            ["-mno-implicit-float"] = true,
            ["-fno-use-init-array"] = true,
            ["-ffixed-point"] = true,
            ["-extract-api"] = true,
            ["-mlong-double-80"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-iprefix"] = true,
            ["-fdigraphs"] = true,
            ["-mno-packets"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fcall-saved-x9"] = true,
            ["-idirafter"] = true,
            ["-save-stats"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fstack-usage"] = true,
            ["-meabi"] = true,
            ["-Tdata"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mno-movt"] = true,
            ["-fcolor-diagnostics"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-isystem-after"] = true,
            ["-module-file-info"] = true,
            ["-U"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-nobuiltininc"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fxray-instrument"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["--analyzer-output"] = true,
            ["-fno-declspec"] = true,
            ["-print-target-triple"] = true,
            ["-foffload-lto"] = true,
            ["-fseh-exceptions"] = true,
            ["--gpu-bundle-output"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-ffixed-x22"] = true,
            ["-fmodules-ts"] = true,
            ["-gdwarf-5"] = true,
            ["-print-multiarch"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-ffixed-x3"] = true,
            ["-finline-functions"] = true,
            ["-fno-fixed-point"] = true,
            ["--migrate"] = true,
            ["-ffixed-x4"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-w"] = true,
            ["-include-pch"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-mms-bitfields"] = true,
            ["-mno-nvs"] = true,
            ["-fno-profile-generate"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fenable-matrix"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-print-search-dirs"] = true,
            ["-ffixed-x30"] = true,
            ["-Xassembler"] = true,
            ["-fwasm-exceptions"] = true,
            ["-finline-hint-functions"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fcall-saved-x18"] = true,
            ["-T"] = true,
            ["-mgpopt"] = true,
            ["-mno-abicalls"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-nogpulib"] = true,
            ["-rpath"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fno-discard-value-names"] = true,
            ["-mextern-sdata"] = true,
            ["-Xclang"] = true,
            ["-ffixed-x17"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-ffast-math"] = true,
            ["-Qn"] = true,
            ["-fpch-debuginfo"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-Tbss"] = true,
            ["-fgnu89-inline"] = true,
            ["-ffixed-x5"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fverbose-asm"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-dI"] = true,
            ["-mno-neg-immediates"] = true,
            ["-iwithsysroot"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mqdsp6-compat"] = true,
            ["-gdwarf-4"] = true,
            ["-mcmse"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-dsym-dir"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-ffunction-sections"] = true,
            ["-fno-addrsig"] = true,
            ["-pipe"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mno-outline-atomics"] = true,
            ["-mbackchain"] = true,
            ["-ffixed-a1"] = true,
            ["-fsystem-module"] = true,
            ["-traditional-cpp"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-gline-directives-only"] = true,
            ["-fno-jump-tables"] = true,
            ["-fno-global-isel"] = true,
            ["-fblocks"] = true,
            ["-mno-mt"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-pedantic"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fvectorize"] = true,
            ["-fmodules-search-all"] = true,
            ["-H"] = true,
            ["-fpascal-strings"] = true,
            ["-pg"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fexceptions"] = true,
            ["--cuda-host-only"] = true,
            ["-mno-embedded-data"] = true,
            ["-mpackets"] = true,
            ["-frwpi"] = true,
            ["--verify-debug-info"] = true,
            ["-fansi-escape-codes"] = true,
            ["-mhvx-qfloat"] = true,
            ["-M"] = true,
            ["-mno-msa"] = true,
            ["-funroll-loops"] = true,
            ["-ffixed-x29"] = true,
            ["-ffixed-x2"] = true,
            ["-mcumode"] = true,
            ["-mno-tgsplit"] = true,
            ["-mno-seses"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-strict-return"] = true,
            ["-cl-no-stdinc"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fsanitize-stats"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fsigned-char"] = true,
            ["-fdeclspec"] = true,
            ["-mnop-mcount"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fcf-protection"] = true,
            ["-mno-crc"] = true,
            ["-fsplit-stack"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fuse-line-directives"] = true,
            ["-emit-llvm"] = true,
            ["-ffixed-d4"] = true,
            ["-o"] = true,
            ["-ffixed-x12"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-mlong-double-64"] = true,
            ["-static-openmp"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-gdwarf"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-ffixed-d1"] = true,
            ["-msoft-float"] = true,
            ["-fgpu-sanitize"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fminimize-whitespace"] = true,
            ["-mno-restrict-it"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-L"] = true,
            ["-nostdinc"] = true,
            ["-ffixed-d2"] = true,
            ["-ffixed-d3"] = true,
            ["--analyze"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-mlvi-cfi"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-emit-ast"] = true,
            ["-Wdeprecated"] = true,
            ["-fobjc-weak"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["--hip-link"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-gdwarf64"] = true,
            ["-fno-unique-section-names"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-access-control"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fcxx-modules"] = true,
            ["--help-hidden"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-MG"] = true,
            ["-F"] = true,
            ["-ffixed-x9"] = true,
            ["-mexecute-only"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fno-finite-loops"] = true,
            ["-mrecord-mcount"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-faligned-allocation"] = true,
            ["-dependency-dot"] = true,
            ["-malign-double"] = true,
            ["-fno-show-source-location"] = true,
            ["-femulated-tls"] = true,
            ["-ffixed-x1"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-mmadd4"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-ffixed-a0"] = true,
            ["-freroll-loops"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fzvector"] = true,
            ["-dM"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-CC"] = true,
            ["-ffixed-x10"] = true,
            ["-ffixed-d7"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fropi"] = true,
            ["-ffixed-x20"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-mrestrict-it"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fno-pch-codegen"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-print-supported-cpus"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-mtgsplit"] = true,
            ["-fms-hotpatch"] = true,
            ["-ffixed-r9"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-isysroot"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fopenmp-simd"] = true,
            ["-include"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fpch-codegen"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fgnu-keywords"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fcall-saved-x8"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-cxx-isystem"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-Xanalyzer"] = true,
            ["-pthread"] = true,
            ["-ffixed-a5"] = true,
            ["-mno-hvx"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-elide-type"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fno-new-infallible"] = true,
            ["-ffixed-a2"] = true,
            ["-C"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fcoroutines-ts"] = true,
            ["-ftime-trace"] = true,
            ["-mllvm"] = true,
            ["--config"] = true,
            ["-b"] = true,
            ["-gcodeview-ghash"] = true,
            ["-mlong-calls"] = true,
            ["-ffixed-r19"] = true,
            ["-fsycl"] = true,
            ["-fmodules-decluse"] = true,
            ["-mpacked-stack"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mnvj"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-ffixed-x23"] = true,
            ["-cl-finite-math-only"] = true,
            ["-ffixed-d5"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mabicalls"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-ftrigraphs"] = true,
            ["-ffixed-x19"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fstack-size-section"] = true,
            ["-fno-cxx-modules"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fdata-sections"] = true,
            ["-fcall-saved-x15"] = true,
            ["-mno-relax"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-mlocal-sdata"] = true,
            ["-fshort-enums"] = true,
            ["-mlong-double-128"] = true,
            ["-mfentry"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-MMD"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-isystem"] = true,
            ["-mmark-bti-property"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-munaligned-access"] = true,
            ["-fsanitize-trap"] = true,
            ["-fapple-kext"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-membedded-data"] = true,
            ["-mno-outline"] = true,
            ["-E"] = true,
            ["-gcodeview"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fno-debug-macro"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fno-sycl"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-ffixed-x24"] = true,
            ["-mibt-seal"] = true,
            ["-ibuiltininc"] = true,
            ["-mno-nvj"] = true,
            ["-fno-rtti-data"] = true,
            ["-mhvx"] = true,
            ["-ffixed-x25"] = true,
            ["--precompile"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-finstrument-functions"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-Qunused-arguments"] = true,
            ["-verify-pch"] = true,
            ["-mrelax"] = true,
            ["-fobjc-exceptions"] = true,
            ["-mfp64"] = true,
            ["-time"] = true,
            ["-Ttext"] = true,
            ["-mno-local-sdata"] = true,
            ["-gno-embed-source"] = true,
            ["-freg-struct-return"] = true,
            ["-ffixed-x8"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fstrict-enums"] = true,
            ["-gdwarf-2"] = true,
            ["-B"] = true,
            ["-fjump-tables"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fno-show-column"] = true,
            ["-mno-execute-only"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fobjc-arc"] = true,
            ["-fno-exceptions"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-c"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mrelax-all"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-ffixed-x7"] = true,
            ["-fno-spell-checking"] = true,
            ["-fno-temp-file"] = true,
            ["-fno-signed-char"] = true,
            ["-fmath-errno"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fnew-infallible"] = true,
            ["-ffixed-x6"] = true,
            ["-G"] = true,
            ["-P"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-mno-cumode"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fslp-vectorize"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-split-stack"] = true,
            ["-mmt"] = true,
            ["-fprotect-parens"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fapprox-func"] = true,
            ["-dD"] = true,
            ["-fno-autolink"] = true,
            ["-fno-rtti"] = true,
            ["-fforce-enable-int128"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-undef"] = true,
            ["-fcall-saved-x13"] = true,
            ["-gdwarf-3"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fno-memory-profile"] = true,
            ["--version"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fgpu-rdc"] = true,
            ["-ffixed-x27"] = true,
            ["-index-header-map"] = true,
            ["-D"] = true,
            ["-v"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-mwavefrontsize64"] = true,
            ["-mthread-model"] = true,
            ["-fembed-bitcode"] = true,
            ["-working-directory"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-mcode-object-v3"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-print-resource-dir"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-ffixed-d0"] = true,
            ["-MM"] = true,
            ["-fcall-saved-x11"] = true,
            ["-ffixed-a3"] = true,
            ["-fintegrated-as"] = true,
            ["-save-temps"] = true,
            ["-fno-unroll-loops"] = true,
            ["-mrtd"] = true,
            ["-MT"] = true,
            ["-fno-short-wchar"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mnocrc"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-Qy"] = true,
            ["-ffixed-d6"] = true,
            ["-mno-long-calls"] = true,
            ["-fasync-exceptions"] = true,
            ["-ffinite-loops"] = true,
            ["-gdwarf32"] = true,
            ["-ffixed-x21"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-MV"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-trigraphs"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-I"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fopenmp"] = true,
            ["-ffixed-a4"] = true,
            ["-fdebug-types-section"] = true,
            ["-fxray-link-deps"] = true,
            ["-ffixed-x18"] = true,
            ["-Xlinker"] = true,
            ["-mmemops"] = true,
            ["--no-cuda-version-check"] = true,
            ["-print-runtime-dir"] = true,
            ["-fno-lto"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-plt"] = true,
            ["-fms-compatibility"] = true,
            ["-fno-offload-lto"] = true,
            ["-msave-restore"] = true,
            ["-mno-unaligned-access"] = true,
            ["-nogpuinc"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-gembed-source"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-mglobal-merge"] = true,
            ["-g"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fapplication-extension"] = true,
            ["-Xopenmp-target"] = true,
            ["-ffreestanding"] = true,
            ["-fmemory-profile"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            ndkver = 25,
            cross = "arm-linux-androideabi-",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21"
        }
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    find_program = {
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]],
        zig = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        nim = false,
        clang = false,
        tar = [[C:\Windows\System32\tar.exe]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}