{
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            license = "MIT",
            version = "v3.11.3",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            }
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            license = "MIT",
            links = {
                "fmt"
            },
            static = true,
            version = "10.2.1"
        }
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    find_program_fetch_package_xmake = {
        cmake = false,
        ninja = false
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sdkver = "21",
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]]
        }
    },
    find_program = {
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        clang = false,
        nim = false,
        tar = [[C:\Windows\System32\tar.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]],
        zig = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]]
    }
}