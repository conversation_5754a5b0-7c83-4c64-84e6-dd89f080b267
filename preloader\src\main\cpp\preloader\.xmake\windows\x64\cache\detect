{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--allow-multiple-definition"] = true,
            ["-L"] = true,
            ["--no-insert-timestamp"] = true,
            ["--appcontainer"] = true,
            ["--no-fatal-warnings"] = true,
            ["--tsaware"] = true,
            ["-dy"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--help"] = true,
            ["--disable-no-seh"] = true,
            ["--no-dynamicbase"] = true,
            ["--no-seh"] = true,
            ["--no-demangle"] = true,
            ["--Bdynamic"] = true,
            ["--nxcompat"] = true,
            ["-m"] = true,
            ["--verbose"] = true,
            ["-o"] = true,
            ["--strip-debug"] = true,
            ["--large-address-aware"] = true,
            ["--dynamicbase"] = true,
            ["--version"] = true,
            ["--no-gc-sections"] = true,
            ["--high-entropy-va"] = true,
            ["--kill-at"] = true,
            ["--disable-dynamicbase"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--exclude-all-symbols"] = true,
            ["--strip-all"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--gc-sections"] = true,
            ["-dn"] = true,
            ["--whole-archive"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-S"] = true,
            ["--export-all-symbols"] = true,
            ["--insert-timestamp"] = true,
            ["--enable-auto-import"] = true,
            ["-v"] = true,
            ["--disable-nxcompat"] = true,
            ["--shared"] = true,
            ["--disable-tsaware"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--disable-auto-import"] = true,
            ["--Bstatic"] = true,
            ["-l"] = true,
            ["--no-whole-archive"] = true,
            ["--fatal-warnings"] = true,
            ["-s"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["-static"] = true,
            ["--demangle"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true
    },
    find_program = {
        clang = false,
        nim = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        zig = false,
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]]
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3"
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            links = {
                "fmt"
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            license = "MIT",
            version = "10.2.1",
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            static = true,
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            }
        }
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fintegrated-cc1"] = true,
            ["-MD"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-ffixed-a6"] = true,
            ["-mlocal-sdata"] = true,
            ["-iwithsysroot"] = true,
            ["-print-supported-cpus"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-ffixed-a1"] = true,
            ["-Qn"] = true,
            ["-fdata-sections"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-ffixed-d6"] = true,
            ["-MT"] = true,
            ["-mmsa"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-flto"] = true,
            ["-fno-operator-names"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-MF"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-mfp64"] = true,
            ["-gcodeview"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-nobuiltininc"] = true,
            ["-fjump-tables"] = true,
            ["-dsym-dir"] = true,
            ["-index-header-map"] = true,
            ["-Ttext"] = true,
            ["-mcode-object-v3"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fverbose-asm"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fmodules"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fdebug-macro"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fenable-matrix"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-gdwarf-5"] = true,
            ["-mno-outline-atomics"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fno-use-init-array"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fobjc-weak"] = true,
            ["-fshort-wchar"] = true,
            ["-mexecute-only"] = true,
            ["-gdwarf-2"] = true,
            ["-MG"] = true,
            ["-gmodules"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-mgpopt"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-ffixed-x24"] = true,
            ["-mno-outline"] = true,
            ["-nostdinc"] = true,
            ["-C"] = true,
            ["-MM"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-ffixed-x6"] = true,
            ["-ffixed-x19"] = true,
            ["-iprefix"] = true,
            ["-fdeclspec"] = true,
            ["-mtgsplit"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fcall-saved-x15"] = true,
            ["-ffixed-x11"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-mmemops"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-print-effective-triple"] = true,
            ["-fno-stack-protector"] = true,
            ["-mrelax"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-ffixed-d1"] = true,
            ["-mno-gpopt"] = true,
            ["-c"] = true,
            ["-I-"] = true,
            ["-print-runtime-dir"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-relocatable-pch"] = true,
            ["-MV"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-emit-llvm"] = true,
            ["-fmemory-profile"] = true,
            ["--analyze"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-ffinite-loops"] = true,
            ["-fno-sanitize-stats"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-nohipwrapperinc"] = true,
            ["-traditional-cpp"] = true,
            ["-fno-exceptions"] = true,
            ["-b"] = true,
            ["-fborland-extensions"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-working-directory"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fsized-deallocation"] = true,
            ["-fzvector"] = true,
            ["-mno-nvs"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-B"] = true,
            ["-fapple-kext"] = true,
            ["-imacros"] = true,
            ["-mmark-bti-property"] = true,
            ["-funroll-loops"] = true,
            ["-module-file-info"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-finstrument-functions"] = true,
            ["-ffixed-x31"] = true,
            ["-emit-ast"] = true,
            ["-fslp-vectorize"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mno-global-merge"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["--emit-static-lib"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-G"] = true,
            ["-cl-no-stdinc"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fcxx-exceptions"] = true,
            ["-ffixed-x21"] = true,
            ["-mlvi-cfi"] = true,
            ["-fmath-errno"] = true,
            ["-pedantic"] = true,
            ["-mrtd"] = true,
            ["-ffixed-d4"] = true,
            ["-mseses"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-ffixed-point"] = true,
            ["-fno-offload-lto"] = true,
            ["-H"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-mnvs"] = true,
            ["-ffixed-x23"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-gdwarf-4"] = true,
            ["-shared-libsan"] = true,
            ["-mno-msa"] = true,
            ["-E"] = true,
            ["-fopenmp-extensions"] = true,
            ["-print-targets"] = true,
            ["-fignore-exceptions"] = true,
            ["-emit-merged-ifs"] = true,
            ["--migrate"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fms-extensions"] = true,
            ["--hip-link"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fembed-bitcode"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-ffixed-x3"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mmadd4"] = true,
            ["-fsanitize-stats"] = true,
            ["-ffixed-d2"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-short-wchar"] = true,
            ["-ffixed-x22"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-femit-all-decls"] = true,
            ["-mno-implicit-float"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fno-rtti"] = true,
            ["-fcoverage-mapping"] = true,
            ["-MQ"] = true,
            ["-fno-unroll-loops"] = true,
            ["-moutline"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-ffixed-x2"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-mhvx-qfloat"] = true,
            ["-nogpulib"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-mlvi-hardening"] = true,
            ["-print-target-triple"] = true,
            ["-ffixed-d5"] = true,
            ["-fsave-optimization-record"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-faligned-allocation"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-freroll-loops"] = true,
            ["-mno-nvj"] = true,
            ["-mcmse"] = true,
            ["-ffast-math"] = true,
            ["-ffixed-a5"] = true,
            ["-Xanalyzer"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-mlong-double-80"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-mms-bitfields"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-rewrite-objc"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fprotect-parens"] = true,
            ["-fapplication-extension"] = true,
            ["-fprofile-generate"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-Tbss"] = true,
            ["-extract-api"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-include-pch"] = true,
            ["-fno-strict-return"] = true,
            ["-msave-restore"] = true,
            ["-dependency-dot"] = true,
            ["-frwpi"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mno-memops"] = true,
            ["-I"] = true,
            ["-fno-sycl"] = true,
            ["-fforce-enable-int128"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["--cuda-device-only"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-rpath"] = true,
            ["-fnew-infallible"] = true,
            ["-Tdata"] = true,
            ["-Wdeprecated"] = true,
            ["-fstrict-enums"] = true,
            ["-mrecord-mcount"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mno-packets"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fapprox-func"] = true,
            ["-fno-elide-constructors"] = true,
            ["-munaligned-access"] = true,
            ["-fno-global-isel"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-ffixed-r9"] = true,
            ["-w"] = true,
            ["-fminimize-whitespace"] = true,
            ["--help-hidden"] = true,
            ["-undef"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fno-common"] = true,
            ["-mstack-arg-probe"] = true,
            ["-freciprocal-math"] = true,
            ["-gline-directives-only"] = true,
            ["-fconvergent-functions"] = true,
            ["-fgnu-keywords"] = true,
            ["-fgpu-rdc"] = true,
            ["-gline-tables-only"] = true,
            ["-isystem-after"] = true,
            ["-x"] = true,
            ["-fintegrated-as"] = true,
            ["-emit-module"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mno-save-restore"] = true,
            ["-fgnu-runtime"] = true,
            ["-mqdsp6-compat"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-faddrsig"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-mno-relax"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["--precompile"] = true,
            ["-mno-tgsplit"] = true,
            ["-fno-temp-file"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-help"] = true,
            ["-mcumode"] = true,
            ["-fstack-protector"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-gdwarf-3"] = true,
            ["-ffixed-d3"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-ffixed-a2"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fno-show-source-location"] = true,
            ["-mstackrealign"] = true,
            ["-print-resource-dir"] = true,
            ["-ffixed-x18"] = true,
            ["-Xopenmp-target"] = true,
            ["-mnop-mcount"] = true,
            ["--analyzer-output"] = true,
            ["-ffixed-x17"] = true,
            ["-fmodules-decluse"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fpcc-struct-return"] = true,
            ["-mwavefrontsize64"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-print-ivar-layout"] = true,
            ["-ffixed-x16"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-femulated-tls"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fvectorize"] = true,
            ["-fno-standalone-debug"] = true,
            ["-pthread"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-trigraphs"] = true,
            ["--config"] = true,
            ["-mthread-model"] = true,
            ["-arch"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-ffixed-r19"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-msvr4-struct-return"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fcall-saved-x14"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-membedded-data"] = true,
            ["-mno-extern-sdata"] = true,
            ["-P"] = true,
            ["--verify-debug-info"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-mno-seses"] = true,
            ["-fcall-saved-x13"] = true,
            ["-gdwarf32"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-ftrigraphs"] = true,
            ["-mhvx"] = true,
            ["-mno-code-object-v3"] = true,
            ["-mbackchain"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mlong-double-128"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fsplit-stack"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-ffixed-a4"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-MP"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-static-openmp"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ibuiltininc"] = true,
            ["-ffreestanding"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-ffixed-x29"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-D"] = true,
            ["-fno-digraphs"] = true,
            ["-S"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fxray-instrument"] = true,
            ["-mno-local-sdata"] = true,
            ["-mpacked-stack"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-moutline-atomics"] = true,
            ["-ffixed-x25"] = true,
            ["-save-temps"] = true,
            ["-v"] = true,
            ["-fno-autolink"] = true,
            ["-ffixed-x8"] = true,
            ["-mextern-sdata"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-builtin"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-mibt-seal"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fseh-exceptions"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-lto"] = true,
            ["-fcs-profile-generate"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-declspec"] = true,
            ["-fno-debug-macro"] = true,
            ["-fstack-protector-strong"] = true,
            ["-ffixed-x12"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-jump-tables"] = true,
            ["-mno-restrict-it"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fsycl"] = true,
            ["-mnvj"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fcxx-modules"] = true,
            ["-finline-hint-functions"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fxray-link-deps"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fropi"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-o"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-L"] = true,
            ["-mrestrict-it"] = true,
            ["-mpackets"] = true,
            ["-fno-spell-checking"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fstack-clash-protection"] = true,
            ["-mlong-calls"] = true,
            ["-fgnu89-inline"] = true,
            ["-fms-compatibility"] = true,
            ["-fcall-saved-x10"] = true,
            ["-include"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fansi-escape-codes"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-mabicalls"] = true,
            ["-fno-trigraphs"] = true,
            ["-MMD"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-ffixed-x5"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fcommon"] = true,
            ["-F"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fmodules-ts"] = true,
            ["-fno-access-control"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fdigraphs"] = true,
            ["-fno-rtti-data"] = true,
            ["-gembed-source"] = true,
            ["--gpu-bundle-output"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-emit-interface-stubs"] = true,
            ["-msoft-float"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-module-dependency-dir"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-mglobal-merge"] = true,
            ["-ffixed-x27"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-isysroot"] = true,
            ["-ffixed-d7"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fstandalone-debug"] = true,
            ["-fasync-exceptions"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fcall-saved-x12"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fstack-size-section"] = true,
            ["-mlong-double-64"] = true,
            ["-dI"] = true,
            ["-isystem"] = true,
            ["-miamcu"] = true,
            ["-ffixed-x13"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-foffload-lto"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-verify-pch"] = true,
            ["-gdwarf"] = true,
            ["-fmodules-search-all"] = true,
            ["-fexceptions"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-mno-abicalls"] = true,
            ["-U"] = true,
            ["-mfp32"] = true,
            ["-freg-struct-return"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fno-plt"] = true,
            ["-mnocrc"] = true,
            ["-pipe"] = true,
            ["-dD"] = true,
            ["-fuse-line-directives"] = true,
            ["-gdwarf64"] = true,
            ["-fno-show-column"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-print-multiarch"] = true,
            ["-fno-memory-profile"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fms-hotpatch"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mcrc"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-Xlinker"] = true,
            ["-Xpreprocessor"] = true,
            ["-mno-embedded-data"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-iwithprefix"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-cl-opt-disable"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-g"] = true,
            ["-fobjc-arc"] = true,
            ["-dependency-file"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-ffixed-x26"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fno-addrsig"] = true,
            ["-ffixed-x9"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fno-pch-codegen"] = true,
            ["-cl-finite-math-only"] = true,
            ["-ffunction-sections"] = true,
            ["-ftime-trace"] = true,
            ["-fobjc-exceptions"] = true,
            ["-ffixed-x4"] = true,
            ["-fno-profile-generate"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-M"] = true,
            ["--no-cuda-version-check"] = true,
            ["-idirafter"] = true,
            ["-mllvm"] = true,
            ["-ffixed-x20"] = true,
            ["-pg"] = true,
            ["-Xclang"] = true,
            ["-fblocks"] = true,
            ["-mfentry"] = true,
            ["-nogpuinc"] = true,
            ["-mno-crc"] = true,
            ["-static-libsan"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-z"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fpch-codegen"] = true,
            ["-dM"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-T"] = true,
            ["--version"] = true,
            ["-cl-mad-enable"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-ffixed-x1"] = true,
            ["-fstack-usage"] = true,
            ["-ffixed-x30"] = true,
            ["-fdebug-types-section"] = true,
            ["-ffixed-x10"] = true,
            ["-mno-movt"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-save-stats"] = true,
            ["-mmt"] = true,
            ["-ffixed-d0"] = true,
            ["-fpascal-strings"] = true,
            ["-fsanitize-trap"] = true,
            ["-fno-new-infallible"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fopenmp"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-Xassembler"] = true,
            ["-fcf-protection"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fno-elide-type"] = true,
            ["-ffixed-x7"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mrelax-all"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fopenmp-simd"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-meabi"] = true,
            ["-mno-cumode"] = true,
            ["-mno-long-calls"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-Qy"] = true,
            ["-mno-madd4"] = true,
            ["-ffixed-a3"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-time"] = true,
            ["-iquote"] = true,
            ["-cxx-isystem"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fstack-protector-all"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fshort-enums"] = true,
            ["-fno-finite-loops"] = true,
            ["-CC"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fsigned-char"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-ffixed-x28"] = true,
            ["-mno-hvx"] = true,
            ["-MJ"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-xray-function-index"] = true,
            ["--cuda-host-only"] = true,
            ["-ftrapv"] = true,
            ["-fsystem-module"] = true,
            ["-ffixed-x15"] = true,
            ["-mno-mt"] = true,
            ["-finline-functions"] = true,
            ["-ffixed-a0"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-signed-char"] = true,
            ["-fno-split-stack"] = true,
            ["-malign-double"] = true,
            ["-objcmt-migrate-annotation"] = true
        }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            cross = "arm-linux-androideabi-",
            sdkver = "21"
        }
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    }
}