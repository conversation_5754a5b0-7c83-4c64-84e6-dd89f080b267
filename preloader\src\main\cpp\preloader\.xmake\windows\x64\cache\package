{
    nlohmann_json = {
        __requirestr = "nlohmann_json v3.11.3",
        license = "MIT",
        version = "v3.11.3",
        sysincludedirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]],
        installdir = [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6]],
        __enabled = true
    },
    fmt = {
        installdir = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36]],
        version = "10.2.1",
        links = "fmt",
        sysincludedirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]],
        linkdirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]],
        __requirestr = "fmt 10",
        license = "MIT",
        libfiles = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]],
        static = true,
        envs = {
            PATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\ninja\v1.12.1\f99acdf63a7d4fc88c165b22351691b6\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\c\cmake\4.0.3\5249d11949644178abe2f58a6ffb0624\bin]]
            }
        },
        __enabled = true
    }
}