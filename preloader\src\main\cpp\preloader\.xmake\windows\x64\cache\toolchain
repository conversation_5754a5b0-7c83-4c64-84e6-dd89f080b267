{
    swift_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    zig_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        plat = "android",
        __checked = true,
        arch = "armeabi-v7a"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        plat = "android",
        __checked = true,
        arch = "armeabi-v7a"
    },
    nim_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    clang_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    fpc_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        ndkver = 25,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        __global = true,
        cross = "arm-linux-androideabi-",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android",
        ndk_sdkver = "21",
        arch = "armeabi-v7a"
    },
    rust_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    gfortran_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    go_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    nasm_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    yasm_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    cuda_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    }
}