{
    swift_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    yasm_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    nim_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        __checked = true,
        arch = "armeabi-v7a"
    },
    zig_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64"
    },
    cuda_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    go_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    fpc_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    gfortran_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    nasm_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        __checked = true,
        arch = "armeabi-v7a"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __global = true,
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndkver = 25,
        ndk_sdkver = "21",
        __checked = true,
        cross = "arm-linux-androideabi-",
        plat = "android",
        arch = "armeabi-v7a",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]]
    },
    rust_arch_x64_plat_windows = {
        plat = "windows",
        __checked = true,
        arch = "x64"
    },
    clang_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64"
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    }
}