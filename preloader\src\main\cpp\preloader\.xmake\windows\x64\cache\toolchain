{
    clang_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    gfortran_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    zig_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    },
    nasm_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    msvc_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndk_sdkver = "21",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        arch = "armeabi-v7a",
        cross = "arm-linux-androideabi-",
        ndkver = 25,
        plat = "android",
        __global = true,
        __checked = true
    },
    cuda_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        plat = "android",
        __global = true,
        __checked = true
    },
    go_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    rust_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    nim_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    },
    yasm_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    fpc_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        plat = "android",
        __global = true,
        __checked = true
    },
    swift_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    }
}